<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="com.cyberwolf.hz5" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_splash_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="14"/></Target><Target id="@+id/tv_ascii_logo" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="44"/></Target><Target id="@+id/tv_app_title" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="28" endOffset="43"/></Target><Target id="@+id/tv_app_tagline" view="TextView"><Expressions/><location startLine="31" startOffset="4" endLine="37" endOffset="44"/></Target><Target id="@+id/tv_loading_text" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="53" endOffset="44"/></Target><Target id="@+id/tv_loading_dots" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="63" endOffset="46"/></Target><Target id="@+id/tv_progress_bar" view="TextView"><Expressions/><location startLine="74" startOffset="8" endLine="82" endOffset="38"/></Target><Target id="@+id/tv_developer_info" view="TextView"><Expressions/><location startLine="93" startOffset="4" endLine="98" endOffset="45"/></Target><Target id="@+id/tv_developer_title" view="TextView"><Expressions/><location startLine="100" startOffset="4" endLine="106" endOffset="40"/></Target><Target id="@+id/tv_version" view="TextView"><Expressions/><location startLine="109" startOffset="4" endLine="117" endOffset="41"/></Target></Targets></Layout>