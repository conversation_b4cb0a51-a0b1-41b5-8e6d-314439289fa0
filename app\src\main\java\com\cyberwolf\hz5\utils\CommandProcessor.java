package com.cyberwolf.hz5.utils;

import android.content.Context;
import android.util.Log;

import com.cyberwolf.hz5.HZ5Application;
import com.cyberwolf.hz5.models.TerminalMessage;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Command Processor for H_Z_5 Terminal CyberSuite
 * Handles terminal command execution and simulation
 */
public class CommandProcessor {
    private static final String TAG = "CommandProcessor";
    
    private Context context;
    private CommandProcessorListener listener;
    private boolean isSafeMode;
    private Random random;
    
    public interface CommandProcessorListener {
        void onCommandResult(TerminalMessage message);
        void onMultipleResults(List<TerminalMessage> messages);
    }
    
    public CommandProcessor(Context context) {
        this.context = context;
        this.isSafeMode = HZ5Application.getInstance().isSafeModeEnabled();
        this.random = new Random();
    }
    
    public void setListener(CommandProcessorListener listener) {
        this.listener = listener;
    }
    
    public void processCommand(String command) {
        if (command == null || command.trim().isEmpty()) {
            return;
        }
        
        String[] parts = command.trim().split("\\s+");
        String cmd = parts[0].toLowerCase();
        
        switch (cmd) {
            case Constants.CMD_HELP:
                showHelp();
                break;
            case Constants.CMD_SCAN:
                simulateScan(parts);
                break;
            case Constants.CMD_TRACE:
                simulateTrace(parts);
                break;
            case Constants.CMD_INJECT:
                simulateInject(parts);
                break;
            case Constants.CMD_CONNECT:
                simulateConnect(parts);
                break;
            case Constants.CMD_DISCONNECT:
                simulateDisconnect();
                break;
            case Constants.CMD_STATUS:
                showStatus();
                break;
            case Constants.CMD_DEVICES:
                showDevices();
                break;
            case Constants.CMD_THEME:
                changeTheme(parts);
                break;
            case Constants.CMD_SAFE_MODE:
                toggleSafeMode();
                break;
            case Constants.CMD_CLEAR:
                clearTerminal();
                break;
            case Constants.CMD_UPLOAD:
                simulateUpload(parts);
                break;
            case Constants.CMD_EXECUTE:
                simulateExecute(parts);
                break;
            default:
                showError("Unknown command: " + cmd + ". Type 'help' for available commands.");
                break;
        }
    }
    
    private void showHelp() {
        List<TerminalMessage> messages = new ArrayList<>();
        messages.add(new TerminalMessage("=== H_Z_5 Terminal CyberSuite Commands ===", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("Network & Bluetooth:", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("  scan [type]     - Scan for devices/networks", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  connect <addr>  - Connect to device", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  disconnect      - Disconnect current device", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  devices         - List discovered devices", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("CyberSec Operations:", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("  trace <target>  - Trace route to target", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  inject <payload> - Inject payload (safe mode)", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  upload <file>   - Upload script to lab", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  execute <script> - Execute script in lab", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("System:", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("  status          - Show system status", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  theme [name]    - Change terminal theme", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  safemode        - Toggle safe mode", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  clear           - Clear terminal", TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("  help            - Show this help", TerminalMessage.TYPE_COMMAND_OUTPUT));
        
        if (listener != null) {
            listener.onMultipleResults(messages);
        }
    }
    
    private void simulateScan(String[] parts) {
        String scanType = parts.length > 1 ? parts[1] : "bluetooth";
        
        List<TerminalMessage> messages = new ArrayList<>();
        messages.add(new TerminalMessage("Initiating " + scanType + " scan...", TerminalMessage.TYPE_SYSTEM));
        
        // Simulate scanning process
        new Thread(() -> {
            try {
                Thread.sleep(1000);
                
                List<TerminalMessage> results = new ArrayList<>();
                results.add(new TerminalMessage("Scanning for devices...", TerminalMessage.TYPE_COMMAND_OUTPUT));
                
                // Generate fake devices for demonstration
                String[] deviceNames = {"CyberPhone-01", "SecureTab-X1", "HackerLaptop", "IoT-Device-42", "Unknown-BT"};
                String[] macAddresses = {"AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66", "FF:EE:DD:CC:BB:AA", "99:88:77:66:55:44", "12:34:56:78:90:AB"};
                
                for (int i = 0; i < 3 + random.nextInt(3); i++) {
                    int index = random.nextInt(deviceNames.length);
                    int signal = -30 - random.nextInt(50);
                    results.add(new TerminalMessage(String.format("Found: %s [%s] Signal: %ddBm", 
                        deviceNames[index], macAddresses[index], signal), TerminalMessage.TYPE_SUCCESS));
                    
                    if (listener != null) {
                        listener.onMultipleResults(results);
                        results.clear();
                    }
                    Thread.sleep(500);
                }
                
                results.add(new TerminalMessage("Scan completed. " + (3 + random.nextInt(3)) + " devices found.", TerminalMessage.TYPE_SYSTEM));
                if (listener != null) {
                    listener.onMultipleResults(results);
                }
                
            } catch (InterruptedException e) {
                Log.e(TAG, "Scan simulation interrupted", e);
            }
        }).start();
        
        if (listener != null) {
            listener.onMultipleResults(messages);
        }
    }
    
    private void simulateTrace(String[] parts) {
        if (parts.length < 2) {
            showError("Usage: trace <target>");
            return;
        }
        
        String target = parts[1];
        List<TerminalMessage> messages = new ArrayList<>();
        messages.add(new TerminalMessage("Tracing route to " + target + "...", TerminalMessage.TYPE_SYSTEM));
        
        new Thread(() -> {
            try {
                String[] hops = {"***********", "********", "***********", "************", target};
                
                for (int i = 0; i < hops.length; i++) {
                    Thread.sleep(800);
                    int latency = 10 + random.nextInt(100);
                    List<TerminalMessage> result = new ArrayList<>();
                    result.add(new TerminalMessage(String.format("%d  %s  %dms", i + 1, hops[i], latency), 
                        TerminalMessage.TYPE_COMMAND_OUTPUT));
                    
                    if (listener != null) {
                        listener.onMultipleResults(result);
                    }
                }
                
                List<TerminalMessage> final_result = new ArrayList<>();
                final_result.add(new TerminalMessage("Trace complete.", TerminalMessage.TYPE_SUCCESS));
                if (listener != null) {
                    listener.onMultipleResults(final_result);
                }
                
            } catch (InterruptedException e) {
                Log.e(TAG, "Trace simulation interrupted", e);
            }
        }).start();
        
        if (listener != null) {
            listener.onMultipleResults(messages);
        }
    }
    
    private void simulateInject(String[] parts) {
        if (parts.length < 2) {
            showError("Usage: inject <payload>");
            return;
        }
        
        String payload = parts[1];
        
        if (isSafeMode) {
            List<TerminalMessage> messages = new ArrayList<>();
            messages.add(new TerminalMessage("[SAFE MODE] Simulating payload injection: " + payload, TerminalMessage.TYPE_WARNING));
            messages.add(new TerminalMessage("Payload analysis:", TerminalMessage.TYPE_SYSTEM));
            messages.add(new TerminalMessage("  Type: Educational simulation", TerminalMessage.TYPE_COMMAND_OUTPUT));
            messages.add(new TerminalMessage("  Risk: None (Safe mode active)", TerminalMessage.TYPE_SUCCESS));
            messages.add(new TerminalMessage("  Result: Learning environment - No actual execution", TerminalMessage.TYPE_SYSTEM));
            
            if (listener != null) {
                listener.onMultipleResults(messages);
            }
        } else {
            showError("Payload injection disabled. Enable safe mode for educational simulation.");
        }
    }
    
    private void simulateConnect(String[] parts) {
        if (parts.length < 2) {
            showError("Usage: connect <device_address>");
            return;
        }
        
        String address = parts[1];
        List<TerminalMessage> messages = new ArrayList<>();
        messages.add(new TerminalMessage("Attempting connection to " + address + "...", TerminalMessage.TYPE_SYSTEM));
        
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                List<TerminalMessage> result = new ArrayList<>();
                
                if (random.nextBoolean()) {
                    result.add(new TerminalMessage("Connection established with " + address, TerminalMessage.TYPE_SUCCESS));
                    result.add(new TerminalMessage("Secure channel active", TerminalMessage.TYPE_SYSTEM));
                } else {
                    result.add(new TerminalMessage("Connection failed: Device not responding", TerminalMessage.TYPE_ERROR));
                }
                
                if (listener != null) {
                    listener.onMultipleResults(result);
                }
                
            } catch (InterruptedException e) {
                Log.e(TAG, "Connection simulation interrupted", e);
            }
        }).start();
        
        if (listener != null) {
            listener.onMultipleResults(messages);
        }
    }
    
    private void simulateDisconnect() {
        TerminalMessage message = new TerminalMessage("Disconnecting from current device...", TerminalMessage.TYPE_SYSTEM);
        if (listener != null) {
            listener.onCommandResult(message);
        }
        
        new Thread(() -> {
            try {
                Thread.sleep(1000);
                TerminalMessage result = new TerminalMessage("Disconnected successfully", TerminalMessage.TYPE_SUCCESS);
                if (listener != null) {
                    listener.onCommandResult(result);
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "Disconnect simulation interrupted", e);
            }
        }).start();
    }
    
    private void showStatus() {
        List<TerminalMessage> messages = new ArrayList<>();
        messages.add(new TerminalMessage("=== System Status ===", TerminalMessage.TYPE_SYSTEM));
        messages.add(new TerminalMessage("App: " + HZ5Application.APP_NAME, TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("Version: " + HZ5Application.APP_VERSION, TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("Developer: " + HZ5Application.DEVELOPER_NAME, TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("Safe Mode: " + (isSafeMode ? "ENABLED" : "DISABLED"), 
            isSafeMode ? TerminalMessage.TYPE_SUCCESS : TerminalMessage.TYPE_WARNING));
        messages.add(new TerminalMessage("Theme: " + getThemeName(), TerminalMessage.TYPE_COMMAND_OUTPUT));
        messages.add(new TerminalMessage("Bluetooth: " + (isBluetoothAvailable() ? "Available" : "Unavailable"), 
            TerminalMessage.TYPE_COMMAND_OUTPUT));
        
        if (listener != null) {
            listener.onMultipleResults(messages);
        }
    }
    
    private void showDevices() {
        TerminalMessage message = new TerminalMessage("Use 'scan' command to discover devices", TerminalMessage.TYPE_SYSTEM);
        if (listener != null) {
            listener.onCommandResult(message);
        }
    }
    
    private void changeTheme(String[] parts) {
        if (parts.length < 2) {
            List<TerminalMessage> messages = new ArrayList<>();
            messages.add(new TerminalMessage("Available themes:", TerminalMessage.TYPE_SYSTEM));
            messages.add(new TerminalMessage("  matrix  - Matrix Green theme", TerminalMessage.TYPE_COMMAND_OUTPUT));
            messages.add(new TerminalMessage("  red     - Red Shadow theme", TerminalMessage.TYPE_COMMAND_OUTPUT));
            messages.add(new TerminalMessage("  gray    - Cyber Gray theme", TerminalMessage.TYPE_COMMAND_OUTPUT));
            messages.add(new TerminalMessage("Usage: theme <name>", TerminalMessage.TYPE_SYSTEM));
            
            if (listener != null) {
                listener.onMultipleResults(messages);
            }
            return;
        }
        
        String themeName = parts[1].toLowerCase();
        int newTheme = -1;
        
        switch (themeName) {
            case "matrix":
                newTheme = HZ5Application.THEME_MATRIX_GREEN;
                break;
            case "red":
                newTheme = HZ5Application.THEME_RED_SHADOW;
                break;
            case "gray":
                newTheme = HZ5Application.THEME_CYBER_GRAY;
                break;
        }
        
        if (newTheme != -1) {
            HZ5Application.getInstance().setCurrentTheme(newTheme);
            TerminalMessage message = new TerminalMessage("Theme changed to " + themeName, TerminalMessage.TYPE_SUCCESS);
            if (listener != null) {
                listener.onCommandResult(message);
            }
        } else {
            showError("Unknown theme: " + themeName);
        }
    }
    
    private void toggleSafeMode() {
        isSafeMode = !isSafeMode;
        HZ5Application.getInstance().getPreferences().edit().putBoolean(HZ5Application.PREF_SAFE_MODE, isSafeMode).apply();
        
        String status = isSafeMode ? "ENABLED" : "DISABLED";
        String message = "Safe mode " + status + " - " + (isSafeMode ? "Learning environment active" : "Attack simulation active");
        
        TerminalMessage result = new TerminalMessage(message, 
            isSafeMode ? TerminalMessage.TYPE_SUCCESS : TerminalMessage.TYPE_WARNING);
        
        if (listener != null) {
            listener.onCommandResult(result);
        }
    }
    
    private void clearTerminal() {
        // This will be handled by the UI
        TerminalMessage message = new TerminalMessage("Terminal cleared", TerminalMessage.TYPE_SYSTEM);
        if (listener != null) {
            listener.onCommandResult(message);
        }
    }
    
    private void simulateUpload(String[] parts) {
        if (parts.length < 2) {
            showError("Usage: upload <filename>");
            return;
        }
        
        String filename = parts[1];
        TerminalMessage message = new TerminalMessage("Uploading " + filename + " to CyberSec lab...", TerminalMessage.TYPE_SYSTEM);
        if (listener != null) {
            listener.onCommandResult(message);
        }
        
        new Thread(() -> {
            try {
                Thread.sleep(1500);
                TerminalMessage result = new TerminalMessage("Upload completed: " + filename, TerminalMessage.TYPE_SUCCESS);
                if (listener != null) {
                    listener.onCommandResult(result);
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "Upload simulation interrupted", e);
            }
        }).start();
    }
    
    private void simulateExecute(String[] parts) {
        if (parts.length < 2) {
            showError("Usage: execute <script>");
            return;
        }
        
        String script = parts[1];
        
        if (isSafeMode) {
            List<TerminalMessage> messages = new ArrayList<>();
            messages.add(new TerminalMessage("[SAFE MODE] Executing " + script + " in sandbox...", TerminalMessage.TYPE_WARNING));
            messages.add(new TerminalMessage("Simulation output:", TerminalMessage.TYPE_SYSTEM));
            messages.add(new TerminalMessage("  Script loaded successfully", TerminalMessage.TYPE_SUCCESS));
            messages.add(new TerminalMessage("  Educational mode: No actual system changes", TerminalMessage.TYPE_SYSTEM));
            messages.add(new TerminalMessage("  Execution completed safely", TerminalMessage.TYPE_SUCCESS));
            
            if (listener != null) {
                listener.onMultipleResults(messages);
            }
        } else {
            showError("Script execution disabled. Enable safe mode for educational simulation.");
        }
    }
    
    private void showError(String error) {
        TerminalMessage message = new TerminalMessage(error, TerminalMessage.TYPE_ERROR);
        if (listener != null) {
            listener.onCommandResult(message);
        }
    }
    
    private String getThemeName() {
        int theme = HZ5Application.getInstance().getCurrentTheme();
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return "Matrix Green";
            case HZ5Application.THEME_RED_SHADOW:
                return "Red Shadow";
            case HZ5Application.THEME_CYBER_GRAY:
                return "Cyber Gray";
            default:
                return "Unknown";
        }
    }
    
    private boolean isBluetoothAvailable() {
        // This would check actual Bluetooth availability
        return true;
    }
}
