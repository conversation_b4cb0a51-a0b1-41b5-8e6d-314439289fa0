[{"merged": "com.cyberwolf.hz5.app-debug-42:/layout_activity_main.xml.flat", "source": "com.cyberwolf.hz5.app-main-44:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\layout\\activity_main.xml"}, {"merged": "com.cyberwolf.hz5.app-debug-42:/xml_file_paths.xml.flat", "source": "com.cyberwolf.hz5.app-main-44:/xml/file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "com.cyberwolf.hz5.app-debug-42:/layout_activity_bluetooth_scanner.xml.flat", "source": "com.cyberwolf.hz5.app-main-44:/layout/activity_bluetooth_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "com.cyberwolf.hz5.app-debug-42:/layout_activity_splash.xml.flat", "source": "com.cyberwolf.hz5.app-main-44:/layout/activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-debug-42:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.cyberwolf.hz5.app-main-44:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "com.cyberwolf.hz5.app-debug-42:/layout_item_terminal_message.xml.flat", "source": "com.cyberwolf.hz5.app-main-44:/layout/item_terminal_message.xml"}]