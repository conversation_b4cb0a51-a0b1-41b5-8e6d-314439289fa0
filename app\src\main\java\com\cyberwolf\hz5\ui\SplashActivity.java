package com.cyberwolf.hz5.ui;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.cyberwolf.hz5.HZ5Application;
import com.cyberwolf.hz5.R;
import com.cyberwolf.hz5.utils.Constants;

/**
 * Splash Screen Activity for H_Z_5 Terminal CyberSuite
 * Shows app branding and developer information with terminal-style animations
 */
public class SplashActivity extends AppCompatActivity {
    private static final String TAG = "SplashActivity";
    
    private TextView tvLoadingDots;
    private TextView tvProgressBar;
    private Handler handler;
    private Runnable loadingAnimation;
    private Runnable progressAnimation;
    private int dotCount = 0;
    private int progressCount = 0;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);
        
        initializeViews();
        startAnimations();
        
        // Navigate to MainActivity after splash duration
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            stopAnimations();
            startActivity(new Intent(SplashActivity.this, MainActivity.class));
            finish();
        }, Constants.SPLASH_DURATION);
    }
    
    private void initializeViews() {
        tvLoadingDots = findViewById(R.id.tv_loading_dots);
        tvProgressBar = findViewById(R.id.tv_progress_bar);
        
        // Set app information
        TextView tvAppTitle = findViewById(R.id.tv_app_title);
        TextView tvAppTagline = findViewById(R.id.tv_app_tagline);
        TextView tvDeveloperInfo = findViewById(R.id.tv_developer_info);
        TextView tvDeveloperTitle = findViewById(R.id.tv_developer_title);
        TextView tvVersion = findViewById(R.id.tv_version);
        
        tvAppTitle.setText(HZ5Application.APP_NAME);
        tvAppTagline.setText(HZ5Application.APP_TAGLINE);
        tvDeveloperInfo.setText("Developed by: " + HZ5Application.DEVELOPER_NAME);
        tvDeveloperTitle.setText(HZ5Application.DEVELOPER_TITLE);
        tvVersion.setText("Version " + HZ5Application.APP_VERSION);
    }
    
    private void startAnimations() {
        handler = new Handler(Looper.getMainLooper());
        
        // Loading dots animation
        loadingAnimation = new Runnable() {
            @Override
            public void run() {
                dotCount = (dotCount + 1) % 4;
                String dots = "";
                for (int i = 0; i < dotCount; i++) {
                    dots += ".";
                }
                tvLoadingDots.setText(dots);
                handler.postDelayed(this, 500);
            }
        };
        
        // Progress bar animation
        progressAnimation = new Runnable() {
            @Override
            public void run() {
                if (progressCount <= 100) {
                    updateProgressBar(progressCount);
                    progressCount += 2;
                    handler.postDelayed(this, 30);
                }
            }
        };
        
        // Start animations
        handler.post(loadingAnimation);
        handler.postDelayed(progressAnimation, 500);
        
        // Add fade-in animation to ASCII logo
        TextView asciiLogo = findViewById(R.id.tv_ascii_logo);
        Animation fadeIn = AnimationUtils.loadAnimation(this, android.R.anim.fade_in);
        fadeIn.setDuration(1000);
        asciiLogo.startAnimation(fadeIn);
    }
    
    private void updateProgressBar(int progress) {
        int totalBars = 40;
        int filledBars = (progress * totalBars) / 100;
        
        StringBuilder progressBar = new StringBuilder("[");
        for (int i = 0; i < totalBars; i++) {
            if (i < filledBars) {
                progressBar.append("█");
            } else {
                progressBar.append(" ");
            }
        }
        progressBar.append("] ").append(progress).append("%");
        
        tvProgressBar.setText(progressBar.toString());
    }
    
    private void stopAnimations() {
        if (handler != null) {
            if (loadingAnimation != null) {
                handler.removeCallbacks(loadingAnimation);
            }
            if (progressAnimation != null) {
                handler.removeCallbacks(progressAnimation);
            }
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopAnimations();
    }
}
