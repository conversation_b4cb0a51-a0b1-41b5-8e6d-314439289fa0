<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="button_background">#333333</color>
    <color name="button_pressed">#555555</color>
    <color name="button_text">#FFFFFF</color>
    <color name="cyber_gray_background">#0F0F0F</color>
    <color name="cyber_gray_bright">#CCCCCC</color>
    <color name="cyber_gray_cyan">#66CCFF</color>
    <color name="cyber_gray_dim">#666666</color>
    <color name="cyber_gray_error">#FF6666</color>
    <color name="cyber_gray_normal">#999999</color>
    <color name="cyber_gray_success">#66FF66</color>
    <color name="cyber_gray_warning">#FFCC66</color>
    <color name="error_background">#330000</color>
    <color name="matrix_background">#000000</color>
    <color name="matrix_green_bright">#00FF00</color>
    <color name="matrix_green_cyan">#00FFCC</color>
    <color name="matrix_green_dim">#008800</color>
    <color name="matrix_green_normal">#00CC00</color>
    <color name="matrix_red">#FF0000</color>
    <color name="matrix_yellow">#FFFF00</color>
    <color name="navigation_bar">#000000</color>
    <color name="red_shadow_background">#0A0000</color>
    <color name="red_shadow_bright">#FF4444</color>
    <color name="red_shadow_cyan">#FF6666</color>
    <color name="red_shadow_dim">#882222</color>
    <color name="red_shadow_error">#FF0000</color>
    <color name="red_shadow_normal">#CC3333</color>
    <color name="red_shadow_success">#44FF44</color>
    <color name="red_shadow_warning">#FFAA00</color>
    <color name="splash_accent">#00CCCC</color>
    <color name="splash_background">#000000</color>
    <color name="splash_text">#00FF00</color>
    <color name="status_bar">#000000</color>
    <color name="success_background">#003300</color>
    <color name="terminal_cursor">#FFFFFF</color>
    <color name="terminal_selection">#333333</color>
    <color name="warning_background">#332200</color>
    <color name="white">#FFFFFFFF</color>
    <string name="about_description">H_Z_5 Terminal CyberSuite is a powerful Android application for cybersecurity enthusiasts, ethical hackers, and learners. It features terminal-based Bluetooth messaging, secure file transfers, encryption, and a simulated cybersecurity lab.</string>
    <string name="about_disclaimer">This application is for educational purposes only. Use responsibly and ethically.</string>
    <string name="about_features">Features include real-time Bluetooth messaging, file and video sharing, AES encryption, CyberSec simulation mode, and multiple terminal themes.</string>
    <string name="about_title">About H_Z_5</string>
    <string name="activity_cybersec_lab">CyberSec Lab</string>
    <string name="activity_file_manager">File Manager</string>
    <string name="activity_main">Terminal</string>
    <string name="activity_scanner">Bluetooth Scanner</string>
    <string name="activity_settings">Settings</string>
    <string name="activity_splash">H_Z_5</string>
    <string name="app_name">H_Z_5 Terminal CyberSuite</string>
    <string name="app_tagline">Experience the Power of Cybersecurity in Your Pocket</string>
    <string name="app_version">1.0</string>
    <string name="bluetooth_connected">Connected</string>
    <string name="bluetooth_connecting">Connecting...</string>
    <string name="bluetooth_disconnected">Disconnected</string>
    <string name="bluetooth_not_enabled">Bluetooth not enabled</string>
    <string name="bluetooth_not_supported">Bluetooth not supported</string>
    <string name="bluetooth_permission_required">Bluetooth permission required</string>
    <string name="bluetooth_scan_complete">Scan completed</string>
    <string name="bluetooth_scanning">Scanning for devices...</string>
    <string name="btn_back">Back</string>
    <string name="btn_cancel">Cancel</string>
    <string name="btn_clear">Clear</string>
    <string name="btn_connect">Connect</string>
    <string name="btn_disconnect">Disconnect</string>
    <string name="btn_ok">OK</string>
    <string name="btn_scan">Scan</string>
    <string name="btn_send">Send</string>
    <string name="btn_settings">Settings</string>
    <string name="cybersec_lab_title">CyberSec Laboratory</string>
    <string name="developer_info">Developed by: S. Tamilselvan - Security Researcher, CyberWolf Community</string>
    <string name="developer_name">S. Tamilselvan</string>
    <string name="developer_title">Security Researcher, CyberWolf Community</string>
    <string name="error_connection_failed">Connection failed</string>
    <string name="error_device_not_found">Device not found</string>
    <string name="error_file_not_found">File not found</string>
    <string name="error_invalid_command">Invalid command</string>
    <string name="error_permission_denied">Permission denied</string>
    <string name="execute_script">Execute Script</string>
    <string name="file_select">Select File</string>
    <string name="file_send">Send File</string>
    <string name="file_transfer_completed">Transfer completed</string>
    <string name="file_transfer_failed">Transfer failed</string>
    <string name="file_transfer_progress">Transfer progress: %d%%</string>
    <string name="file_transfer_started">Transfer started</string>
    <string name="menu_about">About</string>
    <string name="menu_bluetooth_scanner">Bluetooth Scanner</string>
    <string name="menu_cybersec_lab">CyberSec Lab</string>
    <string name="menu_exit">Exit</string>
    <string name="menu_file_manager">File Manager</string>
    <string name="menu_settings">Settings</string>
    <string name="safe_mode_disabled">Safe Mode: DISABLED</string>
    <string name="safe_mode_enabled">Safe Mode: ENABLED</string>
    <string name="script_executed">Script executed in sandbox</string>
    <string name="script_uploaded">Script uploaded successfully</string>
    <string name="settings_about">About</string>
    <string name="settings_auto_reconnect">Auto Reconnect</string>
    <string name="settings_safe_mode">Safe Mode</string>
    <string name="settings_sound_effects">Sound Effects</string>
    <string name="settings_theme">Terminal Theme</string>
    <string name="settings_title">Settings</string>
    <string name="status_connected">Connected to %s</string>
    <string name="status_connecting">Connecting...</string>
    <string name="status_disconnected">Disconnected</string>
    <string name="status_ready">Ready</string>
    <string name="status_scanning">Scanning...</string>
    <string name="status_transferring">Transferring file...</string>
    <string name="terminal_clear">Clear</string>
    <string name="terminal_hint">Enter command...</string>
    <string name="terminal_prompt">hz5@cyberwolf:~$ </string>
    <string name="terminal_prompt_attack">hz5@cyberwolf[ATTACK]:~$ </string>
    <string name="terminal_prompt_connected">hz5@cyberwolf[CONNECTED]:~$ </string>
    <string name="terminal_prompt_safe">hz5@cyberwolf[SAFE]:~$ </string>
    <string name="terminal_send">Send</string>
    <string name="theme_changed">Theme changed to %s</string>
    <string name="theme_cyber_gray">Cyber Gray</string>
    <string name="theme_matrix_green">Matrix Green</string>
    <string name="theme_red_shadow">Red Shadow</string>
    <string name="type_help">Type \'help\' for available commands</string>
    <string name="upload_script">Upload Script</string>
    <string name="welcome_message">Welcome to H_Z_5 Terminal CyberSuite</string>
    <style name="Base.Theme.HZ5" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/matrix_green_normal</item>
        <item name="colorPrimaryVariant">@color/matrix_green_dim</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/matrix_green_cyan</item>
        <item name="colorSecondaryVariant">@color/matrix_green_dim</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:colorBackground">@color/matrix_background</item>
        <item name="colorOnBackground">@color/matrix_green_normal</item>
        <item name="colorSurface">@color/matrix_background</item>
        <item name="colorOnSurface">@color/matrix_green_normal</item>
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="SplashDeveloperStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/matrix_green_dim</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">16dp</item>
    </style>
    <style name="SplashSubtitleStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/splash_accent</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">8dp</item>
    </style>
    <style name="SplashTitleStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/splash_text</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="TerminalButtonStyle">
        <item name="android:background">@color/button_background</item>
        <item name="android:textColor">@color/button_text</item>
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">12sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="TerminalCardStyle">
        <item name="android:background">@color/matrix_background</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:padding">4dp</item>
    </style>
    <style name="TerminalInputStyle" parent="TerminalTextStyle">
        <item name="android:textColorHint">@color/matrix_green_dim</item>
        <item name="android:backgroundTint">@color/matrix_green_dim</item>
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="TerminalTextStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/matrix_green_normal</item>
        <item name="android:background">@color/matrix_background</item>
        <item name="android:padding">8dp</item>
    </style>
    <style name="Theme.HZ5" parent="Base.Theme.HZ5"/>
    <style name="Theme.HZ5.Splash" parent="Base.Theme.HZ5">
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style>
    <style name="Theme.HZ5.Terminal" parent="Base.Theme.HZ5">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
</resources>