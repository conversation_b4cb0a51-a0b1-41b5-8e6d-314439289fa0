{"logs": [{"outputFile": "com.cyberwolf.hz5.app-mergeDebugResources-40:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f980bf18c739eb0cd52cd8f63898f20a\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1559,1621,1688,1746,1817,1876,1930,2044,2104,2167,2221,2294,2413,2499,2575,2666,2747,2830,2969,3054,3141,3274,3362,3440,3497,3548,3614,3686,3762,3833,3916,3989,4066,4148,4222,4331,4421,4500,4591,4687,4761,4842,4937,4991,5073,5139,5226,5312,5374,5438,5501,5574,5681,5791,5889,5995,6056,6111,6193,6278,6354", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1554,1616,1683,1741,1812,1871,1925,2039,2099,2162,2216,2289,2408,2494,2570,2661,2742,2825,2964,3049,3136,3269,3357,3435,3492,3543,3609,3681,3757,3828,3911,3984,4061,4143,4217,4326,4416,4495,4586,4682,4756,4837,4932,4986,5068,5134,5221,5307,5369,5433,5496,5569,5676,5786,5884,5990,6051,6106,6188,6273,6349,6426"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,116,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3153,3231,3309,3397,3505,4323,4419,4535,4692,4764,4919,5010,5076,5139,5227,5289,5356,5414,5485,5544,5598,5712,5772,5835,5889,5962,6081,6167,6243,6334,6415,6498,6637,6722,6809,6942,7030,7108,7165,7216,7282,7354,7430,7501,7584,7657,7734,7816,7890,7999,8089,8168,8259,8355,8429,8510,8605,8659,8741,8807,8894,8980,9042,9106,9169,9242,9349,9459,9557,9663,9724,9860,10166,10251,10327", "endLines": "7,35,36,37,38,39,47,48,49,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,116,119,120,121", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "419,3226,3304,3392,3500,3591,4414,4530,4613,4759,4826,5005,5071,5134,5222,5284,5351,5409,5480,5539,5593,5707,5767,5830,5884,5957,6076,6162,6238,6329,6410,6493,6632,6717,6804,6937,7025,7103,7160,7211,7277,7349,7425,7496,7579,7652,7729,7811,7885,7994,8084,8163,8254,8350,8424,8505,8600,8654,8736,8802,8889,8975,9037,9101,9164,9237,9344,9454,9552,9658,9719,9774,9937,10246,10322,10399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\df2a1ed367d524028e3fe2d9edcf6f08\\transformed\\appcompat-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1135,1217,1308,1401,1496,1590,1690,1783,1878,1973,2064,2155,2254,2360,2466,2564,2671,2778,2883,3053,10084", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1130,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,3148,10161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e6fdcfc353900a7662006ef3fe3ca414\\transformed\\preference-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "50,53,115,117,123,124,125", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4618,4831,9779,9942,10505,10674,10759", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "4687,4914,9855,10079,10669,10754,10837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e1a27e7561c144f01ceeff24742ee3b6\\transformed\\core-1.13.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3596,3696,3798,3899,4000,4105,4210,10404", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3691,3793,3894,3995,4100,4205,4318,10500"}}]}]}