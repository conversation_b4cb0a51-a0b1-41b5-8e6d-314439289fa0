<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/matrix_background">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/matrix_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="BLUETOOTH SCANNER"
            android:textColor="@color/matrix_green_bright"
            android:textSize="18sp"
            android:fontFamily="monospace"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/btn_back"
            style="@style/TerminalButtonStyle" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/matrix_green_dim" />

    <!-- Scanner Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <Button
            android:id="@+id/btn_scan"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/btn_scan"
            style="@style/TerminalButtonStyle"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_stop_scan"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="STOP"
            style="@style/TerminalButtonStyle"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Status -->
    <TextView
        android:id="@+id/tv_scan_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/status_ready"
        android:textColor="@color/matrix_green_normal"
        android:textSize="14sp"
        android:fontFamily="monospace"
        android:padding="16dp" />

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/matrix_green_dim" />

    <!-- Device List Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="DISCOVERED DEVICES:"
        android:textColor="@color/matrix_green_bright"
        android:textSize="14sp"
        android:fontFamily="monospace"
        android:textStyle="bold"
        android:padding="16dp"
        android:paddingBottom="8dp" />

    <!-- Device List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_devices"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp"
        android:paddingTop="0dp" />

    <!-- Connection Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/matrix_background">

        <Button
            android:id="@+id/btn_connect"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/btn_connect"
            style="@style/TerminalButtonStyle"
            android:layout_marginEnd="8dp"
            android:enabled="false" />

        <Button
            android:id="@+id/btn_disconnect"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/btn_disconnect"
            style="@style/TerminalButtonStyle"
            android:layout_marginStart="8dp"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
