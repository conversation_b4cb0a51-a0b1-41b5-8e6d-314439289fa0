com.cyberwolf.hz5.app-emoji2-views-helper-1.3.0-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\093a36a042e7595c18462f6d33b35933\transformed\emoji2-views-helper-1.3.0\res
com.cyberwolf.hz5.app-startup-runtime-1.1.1-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a7c4e7f5405e6eba5ce3292ef2988bd\transformed\startup-runtime-1.1.1\res
com.cyberwolf.hz5.app-lifecycle-runtime-2.6.2-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11acec76f4340d5dab5f543bc5a6c650\transformed\lifecycle-runtime-2.6.2\res
com.cyberwolf.hz5.app-lifecycle-livedata-core-ktx-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\197ace4fd89666400821ab6405edc28e\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.cyberwolf.hz5.app-fragment-ktx-1.6.2-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cfc10257d01201f8f7419e4c784c33f\transformed\fragment-ktx-1.6.2\res
com.cyberwolf.hz5.app-viewpager2-1.1.0-beta02-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e3385f1383744f503440d34d10bba67\transformed\viewpager2-1.1.0-beta02\res
com.cyberwolf.hz5.app-appcompat-resources-1.7.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d6c283157d920d0618d785842fef5e1\transformed\appcompat-resources-1.7.1\res
com.cyberwolf.hz5.app-lifecycle-viewmodel-ktx-2.6.2-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dc61e0832b7b5823492bb614e24740e\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.cyberwolf.hz5.app-lifecycle-livedata-core-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50c7d27ad49e2fd85e160d1fa740d420\transformed\lifecycle-livedata-core-2.6.2\res
com.cyberwolf.hz5.app-window-1.0.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\res
com.cyberwolf.hz5.app-savedstate-ktx-1.2.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62ca0af89daa142ca6c19a4b64ae13be\transformed\savedstate-ktx-1.2.1\res
com.cyberwolf.hz5.app-activity-ktx-1.10.1-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\636ffc28acd99a72aa48fafd7be4c0be\transformed\activity-ktx-1.10.1\res
com.cyberwolf.hz5.app-lifecycle-viewmodel-2.6.2-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af8277e5d530355355182a25b1c2143\transformed\lifecycle-viewmodel-2.6.2\res
com.cyberwolf.hz5.app-annotation-experimental-1.4.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c432e43595acca53bab0d5931f466b0\transformed\annotation-experimental-1.4.0\res
com.cyberwolf.hz5.app-emoji2-1.3.0-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\res
com.cyberwolf.hz5.app-recyclerview-1.3.2-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76d052227c1b8486b8aaaf1548726641\transformed\recyclerview-1.3.2\res
com.cyberwolf.hz5.app-core-viewtree-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bbdb819c29b538cc20a489b70be6ec5\transformed\core-viewtree-1.0.0\res
com.cyberwolf.hz5.app-constraintlayout-2.2.1-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e5d0b352d619a625f5332f0b4919f84\transformed\constraintlayout-2.2.1\res
com.cyberwolf.hz5.app-profileinstaller-1.4.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\res
com.cyberwolf.hz5.app-customview-poolingcontainer-1.0.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90f11856331170eef3aa967fb83d3d53\transformed\customview-poolingcontainer-1.0.0\res
com.cyberwolf.hz5.app-drawerlayout-1.1.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9469cb975477b011f72e9ca19b90195e\transformed\drawerlayout-1.1.1\res
com.cyberwolf.hz5.app-slidingpanelayout-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96d2a8fbd04ef44221966c45db546c3b\transformed\slidingpanelayout-1.2.0\res
com.cyberwolf.hz5.app-activity-1.10.1-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a69e30787bcac3d9d482634da5796f03\transformed\activity-1.10.1\res
com.cyberwolf.hz5.app-transition-1.5.0-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51b17dcb9dbaae2776c5455d8f43b4\transformed\transition-1.5.0\res
com.cyberwolf.hz5.app-cardview-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc1a474ed1562887860e0a8c18d7b907\transformed\cardview-1.0.0\res
com.cyberwolf.hz5.app-lifecycle-process-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\res
com.cyberwolf.hz5.app-coordinatorlayout-1.1.0-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c31849d3b3b0b280ce32b9ecad1038dd\transformed\coordinatorlayout-1.1.0\res
com.cyberwolf.hz5.app-savedstate-1.2.1-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4e5d6c4239a785d239c74bef06ebdf8\transformed\savedstate-1.2.1\res
com.cyberwolf.hz5.app-core-ktx-1.13.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61d6a8ed519b1b06601a7f41b239fe8\transformed\core-ktx-1.13.0\res
com.cyberwolf.hz5.app-core-runtime-2.2.0-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d71235691b059965585b8f07683f0cc8\transformed\core-runtime-2.2.0\res
com.cyberwolf.hz5.app-fragment-1.6.2-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7a8b8cdb0956c92ddddd208b0e57a2a\transformed\fragment-1.6.2\res
com.cyberwolf.hz5.app-lifecycle-viewmodel-savedstate-2.6.2-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddf99882b1c3341d3cc0e94b073b3d1d\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.cyberwolf.hz5.app-appcompat-1.7.1-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2a1ed367d524028e3fe2d9edcf6f08\transformed\appcompat-1.7.1\res
com.cyberwolf.hz5.app-lifecycle-runtime-ktx-2.6.2-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df77dd710df0a0ab6bec5d1570962ac5\transformed\lifecycle-runtime-ktx-2.6.2\res
com.cyberwolf.hz5.app-core-1.13.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\res
com.cyberwolf.hz5.app-preference-1.2.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6fdcfc353900a7662006ef3fe3ca414\transformed\preference-1.2.1\res
com.cyberwolf.hz5.app-material-1.12.0-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f980bf18c739eb0cd52cd8f63898f20a\transformed\material-1.12.0\res
com.cyberwolf.hz5.app-lifecycle-livedata-2.6.2-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd2f6452544a20e623be11ba2eff755c\transformed\lifecycle-livedata-2.6.2\res
com.cyberwolf.hz5.app-pngs-38 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\generated\res\pngs\debug
com.cyberwolf.hz5.app-resValues-39 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\generated\res\resValues\debug
com.cyberwolf.hz5.app-packageDebugResources-40 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.cyberwolf.hz5.app-packageDebugResources-41 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.cyberwolf.hz5.app-debug-42 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\intermediates\merged_res\debug\mergeDebugResources
com.cyberwolf.hz5.app-debug-43 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\debug\res
com.cyberwolf.hz5.app-main-44 C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res
