1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cyberwolf.hz5"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Bluetooth permissions -->
12    <uses-permission android:name="android.permission.BLUETOOTH" />
12-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:6:5-68
12-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
13-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
14-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
15-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:9:5-73
15-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:9:22-70
16    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
16-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:10:5-78
16-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:10:22-75
17
18    <!-- Location permission for Bluetooth scanning on Android 6+ -->
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:13:5-81
19-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:13:22-78
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:14:5-79
20-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:14:22-76
21
22    <!-- File and storage permissions -->
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:17:5-80
23-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:17:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:18:5-81
24-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:19:5-20:40
25-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:19:22-79
26
27    <!-- Network permissions for potential future features -->
28    <uses-permission android:name="android.permission.INTERNET" />
28-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:23:5-67
28-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:23:22-64
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:24:5-79
29-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:24:22-76
30
31    <!-- Wake lock for background operations -->
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:27:5-68
32-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:27:22-65
33
34    <!-- Vibration for notifications -->
35    <uses-permission android:name="android.permission.VIBRATE" />
35-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:30:5-66
35-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:30:22-63
36
37    <!-- Bluetooth features -->
38    <uses-feature
38-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:33:5-35:35
39        android:name="android.hardware.bluetooth"
39-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:34:9-50
40        android:required="true" />
40-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:35:9-32
41    <uses-feature
41-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:36:5-38:36
42        android:name="android.hardware.bluetooth_le"
42-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:37:9-53
43        android:required="false" />
43-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:38:9-33
44
45    <permission
45-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.cyberwolf.hz5.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.cyberwolf.hz5.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:40:5-118:19
52        android:name="com.cyberwolf.hz5.HZ5Application"
52-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:41:9-39
53        android:allowBackup="true"
53-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:42:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a27e7561c144f01ceeff24742ee3b6\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:43:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:44:9-54
59        android:icon="@mipmap/ic_launcher"
59-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:45:9-43
60        android:label="@string/app_name"
60-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:46:9-41
61        android:requestLegacyExternalStorage="true"
61-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:50:9-52
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:47:9-54
63        android:supportsRtl="true"
63-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:48:9-35
64        android:theme="@style/Theme.HZ5" >
64-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:49:9-41
65
66        <!-- Splash Screen Activity -->
67        <activity
67-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:54:9-63:20
68            android:name="com.cyberwolf.hz5.ui.SplashActivity"
68-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:55:13-46
69            android:exported="true"
69-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:56:13-36
70            android:screenOrientation="portrait"
70-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:58:13-49
71            android:theme="@style/Theme.HZ5.Splash" >
71-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:57:13-52
72            <intent-filter>
72-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:59:13-62:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:60:17-69
73-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:60:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:61:17-77
75-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:61:27-74
76            </intent-filter>
77        </activity>
78
79        <!-- Main Terminal Activity -->
80        <activity
80-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:66:9-71:58
81            android:name="com.cyberwolf.hz5.ui.MainActivity"
81-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:67:13-44
82            android:exported="false"
82-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:68:13-37
83            android:screenOrientation="portrait"
83-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:70:13-49
84            android:theme="@style/Theme.HZ5.Terminal"
84-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:69:13-54
85            android:windowSoftInputMode="adjustResize" />
85-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:71:13-55
86
87        <!-- Bluetooth Device Scanner Activity -->
88        <activity
88-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:74:9-78:52
89            android:name="com.cyberwolf.hz5.ui.BluetoothScannerActivity"
89-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:75:13-56
90            android:exported="false"
90-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:76:13-37
91            android:screenOrientation="portrait"
91-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:78:13-49
92            android:theme="@style/Theme.HZ5.Terminal" />
92-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:77:13-54
93
94        <!-- File Manager Activity -->
95        <activity
95-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:81:9-85:52
96            android:name="com.cyberwolf.hz5.ui.FileManagerActivity"
96-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:82:13-51
97            android:exported="false"
97-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:83:13-37
98            android:screenOrientation="portrait"
98-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:85:13-49
99            android:theme="@style/Theme.HZ5.Terminal" />
99-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:84:13-54
100
101        <!-- CyberSec Lab Activity -->
102        <activity
102-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:88:9-92:52
103            android:name="com.cyberwolf.hz5.ui.CyberSecLabActivity"
103-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:89:13-51
104            android:exported="false"
104-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:90:13-37
105            android:screenOrientation="portrait"
105-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:92:13-49
106            android:theme="@style/Theme.HZ5.Terminal" />
106-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:91:13-54
107
108        <!-- Settings Activity -->
109        <activity
109-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:95:9-99:52
110            android:name="com.cyberwolf.hz5.ui.SettingsActivity"
110-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:96:13-48
111            android:exported="false"
111-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:97:13-37
112            android:screenOrientation="portrait"
112-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:99:13-49
113            android:theme="@style/Theme.HZ5.Terminal" />
113-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:98:13-54
114
115        <!-- File Provider for sharing files -->
116        <provider
116-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:102:9-110:20
117            android:name="androidx.core.content.FileProvider"
117-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:103:13-62
118            android:authorities="com.cyberwolf.hz5.fileprovider"
118-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:104:13-65
119            android:exported="false"
119-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:105:13-37
120            android:grantUriPermissions="true" >
120-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:106:13-47
121            <meta-data
121-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:107:13-109:54
122                android:name="android.support.FILE_PROVIDER_PATHS"
122-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:108:17-67
123                android:resource="@xml/file_paths" />
123-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:109:17-51
124        </provider>
125
126        <!-- Bluetooth Service -->
127        <service
127-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:113:9-116:40
128            android:name="com.cyberwolf.hz5.service.BluetoothService"
128-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:114:13-53
129            android:enabled="true"
129-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:115:13-35
130            android:exported="false" />
130-->C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\AndroidManifest.xml:116:13-37
131
132        <provider
132-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
133            android:name="androidx.startup.InitializationProvider"
133-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
134            android:authorities="com.cyberwolf.hz5.androidx-startup"
134-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
135            android:exported="false" >
135-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
136            <meta-data
136-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.emoji2.text.EmojiCompatInitializer"
137-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
138                android:value="androidx.startup" />
138-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6def697dde4c9afda1cc75a39031e773\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
139            <meta-data
139-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
140                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
140-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
141                android:value="androidx.startup" />
141-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21c12642c2c7e44f61eba9d502d25f7\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
142            <meta-data
142-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
143                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
143-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
144                android:value="androidx.startup" />
144-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
145        </provider>
146
147        <uses-library
147-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
148            android:name="androidx.window.extensions"
148-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
149            android:required="false" />
149-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
150        <uses-library
150-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
151            android:name="androidx.window.sidecar"
151-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
152            android:required="false" />
152-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6258324b37ca981a5cf10d38bbb92486\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
153
154        <receiver
154-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
155            android:name="androidx.profileinstaller.ProfileInstallReceiver"
155-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
156            android:directBootAware="false"
156-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
157            android:enabled="true"
157-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
158            android:exported="true"
158-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
159            android:permission="android.permission.DUMP" >
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
161                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
162            </intent-filter>
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
164                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
165            </intent-filter>
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
167                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
168            </intent-filter>
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
170                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7ad180da518ff4536bdcf0a7e3a1d0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
171            </intent-filter>
172        </receiver>
173    </application>
174
175</manifest>
