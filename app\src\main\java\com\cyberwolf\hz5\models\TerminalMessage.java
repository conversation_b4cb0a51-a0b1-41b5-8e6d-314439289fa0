package com.cyberwolf.hz5.models;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Model class for terminal messages in H_Z_5 Terminal CyberSuite
 */
public class TerminalMessage {
    public static final int TYPE_SYSTEM = 0;
    public static final int TYPE_USER_INPUT = 1;
    public static final int TYPE_COMMAND_OUTPUT = 2;
    public static final int TYPE_BLUETOOTH_MESSAGE = 3;
    public static final int TYPE_ERROR = 4;
    public static final int TYPE_SUCCESS = 5;
    public static final int TYPE_WARNING = 6;
    
    private String message;
    private int type;
    private long timestamp;
    private String sender;
    private boolean isEncrypted;
    
    public TerminalMessage(String message, int type) {
        this.message = message;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
        this.sender = null;
        this.isEncrypted = false;
    }
    
    public TerminalMessage(String message, int type, String sender) {
        this.message = message;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
        this.sender = sender;
        this.isEncrypted = false;
    }
    
    public TerminalMessage(String message, int type, String sender, boolean isEncrypted) {
        this.message = message;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
        this.sender = sender;
        this.isEncrypted = isEncrypted;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public int getType() {
        return type;
    }
    
    public void setType(int type) {
        this.type = type;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSender() {
        return sender;
    }
    
    public void setSender(String sender) {
        this.sender = sender;
    }
    
    public boolean isEncrypted() {
        return isEncrypted;
    }
    
    public void setEncrypted(boolean encrypted) {
        isEncrypted = encrypted;
    }
    
    public String getFormattedTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder();
        
        // Add timestamp
        sb.append("[").append(getFormattedTimestamp()).append("] ");
        
        // Add sender if available
        if (sender != null && !sender.isEmpty()) {
            sb.append("<").append(sender).append("> ");
        }
        
        // Add encryption indicator
        if (isEncrypted) {
            sb.append("[ENC] ");
        }
        
        // Add message
        sb.append(message);
        
        return sb.toString();
    }
    
    public String getTypePrefix() {
        switch (type) {
            case TYPE_SYSTEM:
                return "[SYS]";
            case TYPE_USER_INPUT:
                return "[CMD]";
            case TYPE_COMMAND_OUTPUT:
                return "[OUT]";
            case TYPE_BLUETOOTH_MESSAGE:
                return "[BT]";
            case TYPE_ERROR:
                return "[ERR]";
            case TYPE_SUCCESS:
                return "[OK]";
            case TYPE_WARNING:
                return "[WARN]";
            default:
                return "[INFO]";
        }
    }
    
    @Override
    public String toString() {
        return getFormattedMessage();
    }
}
