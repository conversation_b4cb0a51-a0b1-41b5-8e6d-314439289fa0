package com.cyberwolf.hz5.utils;

import android.content.Context;
import android.net.Uri;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * File Transfer Manager for H_Z_5 Terminal CyberSuite
 * Handles file and video sharing operations
 */
public class FileTransferManager {
    private static final String TAG = "FileTransferManager";
    
    private Context context;
    private FileTransferListener listener;
    
    public interface FileTransferListener {
        void onTransferStarted(String fileName);
        void onTransferProgress(int progress);
        void onTransferCompleted(String filePath);
        void onTransferFailed(String error);
    }
    
    public FileTransferManager(Context context) {
        this.context = context;
    }
    
    public void setListener(FileTransferListener listener) {
        this.listener = listener;
    }
    
    /**
     * Get the transfer directory
     */
    public File getTransferDirectory() {
        File transferDir = new File(Environment.getExternalStorageDirectory(), Constants.TRANSFER_FOLDER);
        if (!transferDir.exists()) {
            transferDir.mkdirs();
        }
        return transferDir;
    }
    
    /**
     * Check if file is a supported script type
     */
    public boolean isScriptFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        for (String scriptExt : Constants.SCRIPT_EXTENSIONS) {
            if (extension.equals(scriptExt)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if file is a supported video type
     */
    public boolean isVideoFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        for (String videoExt : Constants.VIDEO_EXTENSIONS) {
            if (extension.equals(videoExt)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if file is a supported document type
     */
    public boolean isDocumentFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        for (String docExt : Constants.DOCUMENT_EXTENSIONS) {
            if (extension.equals(docExt)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get file extension from filename
     */
    public String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot);
        }
        return "";
    }
    
    /**
     * Get file size in bytes
     */
    public long getFileSize(String filePath) {
        File file = new File(filePath);
        return file.exists() ? file.length() : 0;
    }
    
    /**
     * Format file size for display
     */
    public String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * Copy file from URI to transfer directory
     */
    public void copyFileFromUri(Uri uri, String fileName) {
        new Thread(() -> {
            try {
                if (listener != null) {
                    listener.onTransferStarted(fileName);
                }
                
                InputStream inputStream = context.getContentResolver().openInputStream(uri);
                if (inputStream == null) {
                    if (listener != null) {
                        listener.onTransferFailed("Cannot open file");
                    }
                    return;
                }
                
                File transferDir = getTransferDirectory();
                File outputFile = new File(transferDir, fileName);
                
                FileOutputStream outputStream = new FileOutputStream(outputFile);
                
                byte[] buffer = new byte[Constants.BUFFER_SIZE];
                int bytesRead;
                long totalBytes = 0;
                long fileSize = getFileSizeFromUri(uri);
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                    
                    if (fileSize > 0 && listener != null) {
                        int progress = (int) ((totalBytes * 100) / fileSize);
                        listener.onTransferProgress(progress);
                    }
                }
                
                inputStream.close();
                outputStream.close();
                
                if (listener != null) {
                    listener.onTransferCompleted(outputFile.getAbsolutePath());
                }
                
            } catch (IOException e) {
                Log.e(TAG, "File transfer failed", e);
                if (listener != null) {
                    listener.onTransferFailed(e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * Send file via Bluetooth
     */
    public byte[] readFileToBytes(String filePath) {
        try {
            File file = new File(filePath);
            FileInputStream fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            fis.close();
            return data;
        } catch (IOException e) {
            Log.e(TAG, "Error reading file to bytes", e);
            return null;
        }
    }
    
    /**
     * Save received bytes to file
     */
    public void saveBytesToFile(byte[] data, String fileName) {
        new Thread(() -> {
            try {
                if (listener != null) {
                    listener.onTransferStarted(fileName);
                }
                
                File transferDir = getTransferDirectory();
                File outputFile = new File(transferDir, fileName);
                
                FileOutputStream fos = new FileOutputStream(outputFile);
                fos.write(data);
                fos.close();
                
                if (listener != null) {
                    listener.onTransferCompleted(outputFile.getAbsolutePath());
                }
                
            } catch (IOException e) {
                Log.e(TAG, "Error saving bytes to file", e);
                if (listener != null) {
                    listener.onTransferFailed(e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * Get file size from URI
     */
    private long getFileSizeFromUri(Uri uri) {
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(uri);
            if (inputStream != null) {
                long size = inputStream.available();
                inputStream.close();
                return size;
            }
        } catch (IOException e) {
            Log.e(TAG, "Error getting file size from URI", e);
        }
        return 0;
    }
    
    /**
     * Delete file
     */
    public boolean deleteFile(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
    
    /**
     * List files in transfer directory
     */
    public File[] listTransferFiles() {
        File transferDir = getTransferDirectory();
        return transferDir.listFiles();
    }
    
    /**
     * Get MIME type for file
     */
    public String getMimeType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        
        if (isVideoFile(fileName)) {
            return "video/*";
        } else if (isScriptFile(fileName)) {
            return "text/plain";
        } else if (isDocumentFile(fileName)) {
            if (extension.equals(".pdf")) {
                return "application/pdf";
            } else {
                return "application/msword";
            }
        } else {
            return "application/octet-stream";
        }
    }
}
