<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <!-- Location permission for Bluetooth scanning on Android 6+ -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- File and storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <!-- Network permissions for potential future features -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Wake lock for background operations -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Vibration for notifications -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Bluetooth features -->
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false" />

    <application
        android:name=".HZ5Application"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.HZ5"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="31">

        <!-- Splash Screen Activity -->
        <activity
            android:name=".ui.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.HZ5.Splash"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Terminal Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="false"
            android:theme="@style/Theme.HZ5.Terminal"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- Bluetooth Device Scanner Activity -->
        <activity
            android:name=".ui.BluetoothScannerActivity"
            android:exported="false"
            android:theme="@style/Theme.HZ5.Terminal"
            android:screenOrientation="portrait" />

        <!-- File Manager Activity -->
        <activity
            android:name=".ui.FileManagerActivity"
            android:exported="false"
            android:theme="@style/Theme.HZ5.Terminal"
            android:screenOrientation="portrait" />

        <!-- CyberSec Lab Activity -->
        <activity
            android:name=".ui.CyberSecLabActivity"
            android:exported="false"
            android:theme="@style/Theme.HZ5.Terminal"
            android:screenOrientation="portrait" />

        <!-- Settings Activity -->
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.HZ5.Terminal"
            android:screenOrientation="portrait" />

        <!-- File Provider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.cyberwolf.hz5.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Bluetooth Service -->
        <service
            android:name=".service.BluetoothService"
            android:enabled="true"
            android:exported="false" />

    </application>

</manifest>