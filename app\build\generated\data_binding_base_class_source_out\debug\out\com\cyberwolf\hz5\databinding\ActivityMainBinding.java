// Generated by view binder compiler. Do not edit!
package com.cyberwolf.hz5.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.cyberwolf.hz5.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnBluetoothScanner;

  @NonNull
  public final Button btnClear;

  @NonNull
  public final Button btnCybersecLab;

  @NonNull
  public final Button btnFileManager;

  @NonNull
  public final Button btnSend;

  @NonNull
  public final Button btnSettings;

  @NonNull
  public final EditText etCommandInput;

  @NonNull
  public final LinearLayout main;

  @NonNull
  public final RecyclerView rvTerminal;

  @NonNull
  public final TextView tvAppTitle;

  @NonNull
  public final TextView tvConnectionStatus;

  @NonNull
  public final TextView tvPrompt;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull Button btnBluetoothScanner,
      @NonNull Button btnClear, @NonNull Button btnCybersecLab, @NonNull Button btnFileManager,
      @NonNull Button btnSend, @NonNull Button btnSettings, @NonNull EditText etCommandInput,
      @NonNull LinearLayout main, @NonNull RecyclerView rvTerminal, @NonNull TextView tvAppTitle,
      @NonNull TextView tvConnectionStatus, @NonNull TextView tvPrompt) {
    this.rootView = rootView;
    this.btnBluetoothScanner = btnBluetoothScanner;
    this.btnClear = btnClear;
    this.btnCybersecLab = btnCybersecLab;
    this.btnFileManager = btnFileManager;
    this.btnSend = btnSend;
    this.btnSettings = btnSettings;
    this.etCommandInput = etCommandInput;
    this.main = main;
    this.rvTerminal = rvTerminal;
    this.tvAppTitle = tvAppTitle;
    this.tvConnectionStatus = tvConnectionStatus;
    this.tvPrompt = tvPrompt;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_bluetooth_scanner;
      Button btnBluetoothScanner = ViewBindings.findChildViewById(rootView, id);
      if (btnBluetoothScanner == null) {
        break missingId;
      }

      id = R.id.btn_clear;
      Button btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.btn_cybersec_lab;
      Button btnCybersecLab = ViewBindings.findChildViewById(rootView, id);
      if (btnCybersecLab == null) {
        break missingId;
      }

      id = R.id.btn_file_manager;
      Button btnFileManager = ViewBindings.findChildViewById(rootView, id);
      if (btnFileManager == null) {
        break missingId;
      }

      id = R.id.btn_send;
      Button btnSend = ViewBindings.findChildViewById(rootView, id);
      if (btnSend == null) {
        break missingId;
      }

      id = R.id.btn_settings;
      Button btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.et_command_input;
      EditText etCommandInput = ViewBindings.findChildViewById(rootView, id);
      if (etCommandInput == null) {
        break missingId;
      }

      LinearLayout main = (LinearLayout) rootView;

      id = R.id.rv_terminal;
      RecyclerView rvTerminal = ViewBindings.findChildViewById(rootView, id);
      if (rvTerminal == null) {
        break missingId;
      }

      id = R.id.tv_app_title;
      TextView tvAppTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTitle == null) {
        break missingId;
      }

      id = R.id.tv_connection_status;
      TextView tvConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvConnectionStatus == null) {
        break missingId;
      }

      id = R.id.tv_prompt;
      TextView tvPrompt = ViewBindings.findChildViewById(rootView, id);
      if (tvPrompt == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, btnBluetoothScanner, btnClear,
          btnCybersecLab, btnFileManager, btnSend, btnSettings, etCommandInput, main, rvTerminal,
          tvAppTitle, tvConnectionStatus, tvPrompt);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
