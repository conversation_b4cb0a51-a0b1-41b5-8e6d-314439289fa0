plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.cyberwolf.hz5"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.cyberwolf.hz5"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    // Core Android dependencies
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.activity)
    implementation(libs.constraintlayout)

    // RecyclerView for terminal logs
    implementation("androidx.recyclerview:recyclerview:1.3.2")

    // CardView for UI components
    implementation("androidx.cardview:cardview:1.0.0")

    // Fragment support
    implementation("androidx.fragment:fragment:1.6.2")

    // Bluetooth and permissions
    implementation("androidx.core:core:1.12.0")

    // File handling and media
    implementation("androidx.documentfile:documentfile:1.0.1")

    // Animation and transitions
    implementation("androidx.transition:transition:1.4.1")

    // Preferences for settings
    implementation("androidx.preference:preference:1.2.1")

    // Testing dependencies
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
}