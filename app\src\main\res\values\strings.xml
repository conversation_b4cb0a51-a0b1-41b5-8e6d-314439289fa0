<resources>
    <!-- App Info -->
    <string name="app_name">H_Z_5 Terminal CyberSuite</string>
    <string name="app_tagline">Experience the Power of Cybersecurity in Your Pocket</string>
    <string name="developer_name"><PERSON><PERSON></string>
    <string name="developer_title">Security Researcher, CyberWolf Community</string>
    <string name="app_version">1.0</string>

    <!-- Activities -->
    <string name="activity_main">Terminal</string>
    <string name="activity_splash">H_Z_5</string>
    <string name="activity_scanner">Bluetooth Scanner</string>
    <string name="activity_file_manager">File Manager</string>
    <string name="activity_cybersec_lab">CyberSec Lab</string>
    <string name="activity_settings">Settings</string>

    <!-- Terminal -->
    <string name="terminal_prompt">hz5@cyberwolf:~$ </string>
    <string name="terminal_prompt_connected">hz5@cyberwolf[CONNECTED]:~$ </string>
    <string name="terminal_prompt_safe">hz5@cyberwolf[SAFE]:~$ </string>
    <string name="terminal_prompt_attack">hz5@cyberwolf[ATTACK]:~$ </string>
    <string name="terminal_hint">Enter command...</string>
    <string name="terminal_send">Send</string>
    <string name="terminal_clear">Clear</string>

    <!-- Welcome Messages -->
    <string name="welcome_message">Welcome to H_Z_5 Terminal CyberSuite</string>
    <string name="developer_info">Developed by: S. Tamilselvan - Security Researcher, CyberWolf Community</string>
    <string name="type_help">Type \'help\' for available commands</string>

    <!-- Bluetooth -->
    <string name="bluetooth_scanning">Scanning for devices...</string>
    <string name="bluetooth_scan_complete">Scan completed</string>
    <string name="bluetooth_connecting">Connecting...</string>
    <string name="bluetooth_connected">Connected</string>
    <string name="bluetooth_disconnected">Disconnected</string>
    <string name="bluetooth_not_supported">Bluetooth not supported</string>
    <string name="bluetooth_not_enabled">Bluetooth not enabled</string>
    <string name="bluetooth_permission_required">Bluetooth permission required</string>

    <!-- File Transfer -->
    <string name="file_transfer_started">Transfer started</string>
    <string name="file_transfer_progress">Transfer progress: %d%%</string>
    <string name="file_transfer_completed">Transfer completed</string>
    <string name="file_transfer_failed">Transfer failed</string>
    <string name="file_select">Select File</string>
    <string name="file_send">Send File</string>

    <!-- CyberSec Lab -->
    <string name="cybersec_lab_title">CyberSec Laboratory</string>
    <string name="safe_mode_enabled">Safe Mode: ENABLED</string>
    <string name="safe_mode_disabled">Safe Mode: DISABLED</string>
    <string name="upload_script">Upload Script</string>
    <string name="execute_script">Execute Script</string>
    <string name="script_uploaded">Script uploaded successfully</string>
    <string name="script_executed">Script executed in sandbox</string>

    <!-- Themes -->
    <string name="theme_matrix_green">Matrix Green</string>
    <string name="theme_red_shadow">Red Shadow</string>
    <string name="theme_cyber_gray">Cyber Gray</string>
    <string name="theme_changed">Theme changed to %s</string>

    <!-- Settings -->
    <string name="settings_title">Settings</string>
    <string name="settings_theme">Terminal Theme</string>
    <string name="settings_sound_effects">Sound Effects</string>
    <string name="settings_auto_reconnect">Auto Reconnect</string>
    <string name="settings_safe_mode">Safe Mode</string>
    <string name="settings_about">About</string>

    <!-- Buttons -->
    <string name="btn_connect">Connect</string>
    <string name="btn_disconnect">Disconnect</string>
    <string name="btn_scan">Scan</string>
    <string name="btn_send">Send</string>
    <string name="btn_clear">Clear</string>
    <string name="btn_settings">Settings</string>
    <string name="btn_back">Back</string>
    <string name="btn_ok">OK</string>
    <string name="btn_cancel">Cancel</string>

    <!-- Errors -->
    <string name="error_permission_denied">Permission denied</string>
    <string name="error_connection_failed">Connection failed</string>
    <string name="error_file_not_found">File not found</string>
    <string name="error_invalid_command">Invalid command</string>
    <string name="error_device_not_found">Device not found</string>

    <!-- Status Messages -->
    <string name="status_ready">Ready</string>
    <string name="status_connecting">Connecting...</string>
    <string name="status_connected">Connected to %s</string>
    <string name="status_disconnected">Disconnected</string>
    <string name="status_scanning">Scanning...</string>
    <string name="status_transferring">Transferring file...</string>

    <!-- Menu Items -->
    <string name="menu_bluetooth_scanner">Bluetooth Scanner</string>
    <string name="menu_file_manager">File Manager</string>
    <string name="menu_cybersec_lab">CyberSec Lab</string>
    <string name="menu_settings">Settings</string>
    <string name="menu_about">About</string>
    <string name="menu_exit">Exit</string>

    <!-- About -->
    <string name="about_title">About H_Z_5</string>
    <string name="about_description">H_Z_5 Terminal CyberSuite is a powerful Android application for cybersecurity enthusiasts, ethical hackers, and learners. It features terminal-based Bluetooth messaging, secure file transfers, encryption, and a simulated cybersecurity lab.</string>
    <string name="about_features">Features include real-time Bluetooth messaging, file and video sharing, AES encryption, CyberSec simulation mode, and multiple terminal themes.</string>
    <string name="about_disclaimer">This application is for educational purposes only. Use responsibly and ethically.</string>
</resources>