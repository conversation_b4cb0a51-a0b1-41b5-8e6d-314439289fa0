package com.cyberwolf.hz5.utils;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.cyberwolf.hz5.HZ5Application;
import com.cyberwolf.hz5.R;

/**
 * Theme Manager for H_Z_5 Terminal CyberSuite
 * Handles dynamic theme switching between Matrix Green, Red Shadow, and Cyber Gray
 */
public class ThemeManager {
    private static final String TAG = "ThemeManager";
    
    public static void applyTheme(Activity activity, int theme) {
        View rootView = activity.findViewById(android.R.id.content);
        if (rootView != null) {
            applyThemeToView(activity, rootView, theme);
        }
    }
    
    public static void applyThemeToView(Context context, View view, int theme) {
        if (view instanceof TextView) {
            applyThemeToTextView(context, (TextView) view, theme);
        }
        
        // Apply background color based on theme
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                view.setBackgroundColor(ContextCompat.getColor(context, R.color.matrix_background));
                break;
            case HZ5Application.THEME_RED_SHADOW:
                view.setBackgroundColor(ContextCompat.getColor(context, R.color.red_shadow_background));
                break;
            case HZ5Application.THEME_CYBER_GRAY:
                view.setBackgroundColor(ContextCompat.getColor(context, R.color.cyber_gray_background));
                break;
        }
    }
    
    private static void applyThemeToTextView(Context context, TextView textView, int theme) {
        int textColor = getDefaultTextColor(context, theme);
        textView.setTextColor(textColor);
    }
    
    public static int getDefaultTextColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_green_normal);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_normal);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_normal);
            default:
                return ContextCompat.getColor(context, R.color.matrix_green_normal);
        }
    }
    
    public static int getBackgroundColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_background);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_background);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_background);
            default:
                return ContextCompat.getColor(context, R.color.matrix_background);
        }
    }
    
    public static int getBrightTextColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_green_bright);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_bright);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_bright);
            default:
                return ContextCompat.getColor(context, R.color.matrix_green_bright);
        }
    }
    
    public static int getDimTextColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_green_dim);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_dim);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_dim);
            default:
                return ContextCompat.getColor(context, R.color.matrix_green_dim);
        }
    }
    
    public static int getErrorColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_red);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_error);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_error);
            default:
                return ContextCompat.getColor(context, R.color.matrix_red);
        }
    }
    
    public static int getSuccessColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_green_bright);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_success);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_success);
            default:
                return ContextCompat.getColor(context, R.color.matrix_green_bright);
        }
    }
    
    public static int getWarningColor(Context context, int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return ContextCompat.getColor(context, R.color.matrix_yellow);
            case HZ5Application.THEME_RED_SHADOW:
                return ContextCompat.getColor(context, R.color.red_shadow_warning);
            case HZ5Application.THEME_CYBER_GRAY:
                return ContextCompat.getColor(context, R.color.cyber_gray_warning);
            default:
                return ContextCompat.getColor(context, R.color.matrix_yellow);
        }
    }
    
    public static String getThemeName(int theme) {
        switch (theme) {
            case HZ5Application.THEME_MATRIX_GREEN:
                return "Matrix Green";
            case HZ5Application.THEME_RED_SHADOW:
                return "Red Shadow";
            case HZ5Application.THEME_CYBER_GRAY:
                return "Cyber Gray";
            default:
                return "Unknown";
        }
    }
    
    public static int getThemeFromName(String themeName) {
        switch (themeName.toLowerCase()) {
            case "matrix":
            case "matrix green":
                return HZ5Application.THEME_MATRIX_GREEN;
            case "red":
            case "red shadow":
                return HZ5Application.THEME_RED_SHADOW;
            case "gray":
            case "cyber gray":
                return HZ5Application.THEME_CYBER_GRAY;
            default:
                return HZ5Application.THEME_MATRIX_GREEN;
        }
    }
}
