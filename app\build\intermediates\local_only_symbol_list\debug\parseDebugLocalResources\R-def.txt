R_DEF: Internal format may change without notice
local
color black
color button_background
color button_pressed
color button_text
color cyber_gray_background
color cyber_gray_bright
color cyber_gray_cyan
color cyber_gray_dim
color cyber_gray_error
color cyber_gray_normal
color cyber_gray_success
color cyber_gray_warning
color error_background
color matrix_background
color matrix_green_bright
color matrix_green_cyan
color matrix_green_dim
color matrix_green_normal
color matrix_red
color matrix_yellow
color navigation_bar
color red_shadow_background
color red_shadow_bright
color red_shadow_cyan
color red_shadow_dim
color red_shadow_error
color red_shadow_normal
color red_shadow_success
color red_shadow_warning
color splash_accent
color splash_background
color splash_text
color status_bar
color success_background
color terminal_cursor
color terminal_selection
color warning_background
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
id btn_back
id btn_bluetooth_scanner
id btn_clear
id btn_connect
id btn_cybersec_lab
id btn_disconnect
id btn_file_manager
id btn_scan
id btn_send
id btn_settings
id btn_stop_scan
id et_command_input
id main
id rv_devices
id rv_terminal
id tv_app_tagline
id tv_app_title
id tv_ascii_logo
id tv_connection_status
id tv_developer_info
id tv_developer_title
id tv_loading_dots
id tv_loading_text
id tv_message
id tv_progress_bar
id tv_prompt
id tv_scan_status
id tv_version
layout activity_bluetooth_scanner
layout activity_main
layout activity_splash
layout item_terminal_message
mipmap ic_launcher
mipmap ic_launcher_round
string about_description
string about_disclaimer
string about_features
string about_title
string activity_cybersec_lab
string activity_file_manager
string activity_main
string activity_scanner
string activity_settings
string activity_splash
string app_name
string app_tagline
string app_version
string bluetooth_connected
string bluetooth_connecting
string bluetooth_disconnected
string bluetooth_not_enabled
string bluetooth_not_supported
string bluetooth_permission_required
string bluetooth_scan_complete
string bluetooth_scanning
string btn_back
string btn_cancel
string btn_clear
string btn_connect
string btn_disconnect
string btn_ok
string btn_scan
string btn_send
string btn_settings
string cybersec_lab_title
string developer_info
string developer_name
string developer_title
string error_connection_failed
string error_device_not_found
string error_file_not_found
string error_invalid_command
string error_permission_denied
string execute_script
string file_select
string file_send
string file_transfer_completed
string file_transfer_failed
string file_transfer_progress
string file_transfer_started
string menu_about
string menu_bluetooth_scanner
string menu_cybersec_lab
string menu_exit
string menu_file_manager
string menu_settings
string safe_mode_disabled
string safe_mode_enabled
string script_executed
string script_uploaded
string settings_about
string settings_auto_reconnect
string settings_safe_mode
string settings_sound_effects
string settings_theme
string settings_title
string status_connected
string status_connecting
string status_disconnected
string status_ready
string status_scanning
string status_transferring
string terminal_clear
string terminal_hint
string terminal_prompt
string terminal_prompt_attack
string terminal_prompt_connected
string terminal_prompt_safe
string terminal_send
string theme_changed
string theme_cyber_gray
string theme_matrix_green
string theme_red_shadow
string type_help
string upload_script
string welcome_message
style Base.Theme.HZ5
style Base.Theme.H_Z_5
style SplashDeveloperStyle
style SplashSubtitleStyle
style SplashTitleStyle
style TerminalButtonStyle
style TerminalCardStyle
style TerminalInputStyle
style TerminalTextStyle
style Theme.HZ5
style Theme.HZ5.Splash
style Theme.HZ5.Terminal
xml backup_rules
xml data_extraction_rules
xml file_paths
