// Generated by view binder compiler. Do not edit!
package com.cyberwolf.hz5.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.cyberwolf.hz5.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBluetoothScannerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnBack;

  @NonNull
  public final Button btnConnect;

  @NonNull
  public final Button btnDisconnect;

  @NonNull
  public final Button btnScan;

  @NonNull
  public final Button btnStopScan;

  @NonNull
  public final RecyclerView rvDevices;

  @NonNull
  public final TextView tvScanStatus;

  private ActivityBluetoothScannerBinding(@NonNull LinearLayout rootView, @NonNull Button btnBack,
      @NonNull Button btnConnect, @NonNull Button btnDisconnect, @NonNull Button btnScan,
      @NonNull Button btnStopScan, @NonNull RecyclerView rvDevices,
      @NonNull TextView tvScanStatus) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnConnect = btnConnect;
    this.btnDisconnect = btnDisconnect;
    this.btnScan = btnScan;
    this.btnStopScan = btnStopScan;
    this.rvDevices = rvDevices;
    this.tvScanStatus = tvScanStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBluetoothScannerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBluetoothScannerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_bluetooth_scanner, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBluetoothScannerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      Button btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_connect;
      Button btnConnect = ViewBindings.findChildViewById(rootView, id);
      if (btnConnect == null) {
        break missingId;
      }

      id = R.id.btn_disconnect;
      Button btnDisconnect = ViewBindings.findChildViewById(rootView, id);
      if (btnDisconnect == null) {
        break missingId;
      }

      id = R.id.btn_scan;
      Button btnScan = ViewBindings.findChildViewById(rootView, id);
      if (btnScan == null) {
        break missingId;
      }

      id = R.id.btn_stop_scan;
      Button btnStopScan = ViewBindings.findChildViewById(rootView, id);
      if (btnStopScan == null) {
        break missingId;
      }

      id = R.id.rv_devices;
      RecyclerView rvDevices = ViewBindings.findChildViewById(rootView, id);
      if (rvDevices == null) {
        break missingId;
      }

      id = R.id.tv_scan_status;
      TextView tvScanStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvScanStatus == null) {
        break missingId;
      }

      return new ActivityBluetoothScannerBinding((LinearLayout) rootView, btnBack, btnConnect,
          btnDisconnect, btnScan, btnStopScan, rvDevices, tvScanStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
