package com.cyberwolf.hz5.ui.adapters;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.cyberwolf.hz5.HZ5Application;
import com.cyberwolf.hz5.R;
import com.cyberwolf.hz5.models.TerminalMessage;

import java.util.List;

/**
 * RecyclerView adapter for terminal messages in H_Z_5 Terminal CyberSuite
 */
public class TerminalAdapter extends RecyclerView.Adapter<TerminalAdapter.TerminalViewHolder> {
    private List<TerminalMessage> messages;
    private Context context;
    private int currentTheme;
    
    public TerminalAdapter(Context context, List<TerminalMessage> messages) {
        this.context = context;
        this.messages = messages;
        this.currentTheme = HZ5Application.getInstance().getCurrentTheme();
    }
    
    @NonNull
    @Override
    public TerminalViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_terminal_message, parent, false);
        return new TerminalViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TerminalViewHolder holder, int position) {
        TerminalMessage message = messages.get(position);
        holder.bind(message);
    }
    
    @Override
    public int getItemCount() {
        return messages.size();
    }
    
    public void addMessage(TerminalMessage message) {
        messages.add(message);
        notifyItemInserted(messages.size() - 1);
    }
    
    public void clearMessages() {
        messages.clear();
        notifyDataSetChanged();
    }
    
    public void updateTheme(int theme) {
        this.currentTheme = theme;
        notifyDataSetChanged();
    }
    
    class TerminalViewHolder extends RecyclerView.ViewHolder {
        private TextView messageText;
        
        public TerminalViewHolder(@NonNull View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.tv_message);
            
            // Set monospace font for terminal feel
            messageText.setTypeface(Typeface.MONOSPACE);
        }
        
        public void bind(TerminalMessage message) {
            messageText.setText(message.getFormattedMessage());
            
            // Set text color based on message type and theme
            int textColor = getTextColorForMessage(message);
            messageText.setTextColor(textColor);
            
            // Set background for certain message types
            if (message.getType() == TerminalMessage.TYPE_ERROR) {
                messageText.setBackgroundColor(ContextCompat.getColor(context, R.color.error_background));
            } else if (message.getType() == TerminalMessage.TYPE_SUCCESS) {
                messageText.setBackgroundColor(ContextCompat.getColor(context, R.color.success_background));
            } else {
                messageText.setBackgroundColor(ContextCompat.getColor(context, android.R.color.transparent));
            }
        }
        
        private int getTextColorForMessage(TerminalMessage message) {
            switch (currentTheme) {
                case HZ5Application.THEME_MATRIX_GREEN:
                    return getMatrixGreenColor(message);
                case HZ5Application.THEME_RED_SHADOW:
                    return getRedShadowColor(message);
                case HZ5Application.THEME_CYBER_GRAY:
                    return getCyberGrayColor(message);
                default:
                    return getMatrixGreenColor(message);
            }
        }
        
        private int getMatrixGreenColor(TerminalMessage message) {
            switch (message.getType()) {
                case TerminalMessage.TYPE_SYSTEM:
                    return ContextCompat.getColor(context, R.color.matrix_green_bright);
                case TerminalMessage.TYPE_USER_INPUT:
                    return ContextCompat.getColor(context, R.color.matrix_green_normal);
                case TerminalMessage.TYPE_COMMAND_OUTPUT:
                    return ContextCompat.getColor(context, R.color.matrix_green_dim);
                case TerminalMessage.TYPE_BLUETOOTH_MESSAGE:
                    return ContextCompat.getColor(context, R.color.matrix_green_cyan);
                case TerminalMessage.TYPE_ERROR:
                    return ContextCompat.getColor(context, R.color.matrix_red);
                case TerminalMessage.TYPE_SUCCESS:
                    return ContextCompat.getColor(context, R.color.matrix_green_bright);
                case TerminalMessage.TYPE_WARNING:
                    return ContextCompat.getColor(context, R.color.matrix_yellow);
                default:
                    return ContextCompat.getColor(context, R.color.matrix_green_normal);
            }
        }
        
        private int getRedShadowColor(TerminalMessage message) {
            switch (message.getType()) {
                case TerminalMessage.TYPE_SYSTEM:
                    return ContextCompat.getColor(context, R.color.red_shadow_bright);
                case TerminalMessage.TYPE_USER_INPUT:
                    return ContextCompat.getColor(context, R.color.red_shadow_normal);
                case TerminalMessage.TYPE_COMMAND_OUTPUT:
                    return ContextCompat.getColor(context, R.color.red_shadow_dim);
                case TerminalMessage.TYPE_BLUETOOTH_MESSAGE:
                    return ContextCompat.getColor(context, R.color.red_shadow_cyan);
                case TerminalMessage.TYPE_ERROR:
                    return ContextCompat.getColor(context, R.color.red_shadow_error);
                case TerminalMessage.TYPE_SUCCESS:
                    return ContextCompat.getColor(context, R.color.red_shadow_success);
                case TerminalMessage.TYPE_WARNING:
                    return ContextCompat.getColor(context, R.color.red_shadow_warning);
                default:
                    return ContextCompat.getColor(context, R.color.red_shadow_normal);
            }
        }
        
        private int getCyberGrayColor(TerminalMessage message) {
            switch (message.getType()) {
                case TerminalMessage.TYPE_SYSTEM:
                    return ContextCompat.getColor(context, R.color.cyber_gray_bright);
                case TerminalMessage.TYPE_USER_INPUT:
                    return ContextCompat.getColor(context, R.color.cyber_gray_normal);
                case TerminalMessage.TYPE_COMMAND_OUTPUT:
                    return ContextCompat.getColor(context, R.color.cyber_gray_dim);
                case TerminalMessage.TYPE_BLUETOOTH_MESSAGE:
                    return ContextCompat.getColor(context, R.color.cyber_gray_cyan);
                case TerminalMessage.TYPE_ERROR:
                    return ContextCompat.getColor(context, R.color.cyber_gray_error);
                case TerminalMessage.TYPE_SUCCESS:
                    return ContextCompat.getColor(context, R.color.cyber_gray_success);
                case TerminalMessage.TYPE_WARNING:
                    return ContextCompat.getColor(context, R.color.cyber_gray_warning);
                default:
                    return ContextCompat.getColor(context, R.color.cyber_gray_normal);
            }
        }
    }
}
