package com.cyberwolf.hz5.utils;

/**
 * Constants used throughout the H_Z_5 Terminal CyberSuite application
 */
public class Constants {
    
    // Bluetooth constants
    public static final String BLUETOOTH_SERVICE_UUID = "00001101-0000-1000-8000-00805F9B34FB";
    public static final int BLUETOOTH_REQUEST_ENABLE = 1001;
    public static final int LOCATION_PERMISSION_REQUEST = 1002;
    public static final int BLUETOOTH_PERMISSION_REQUEST = 1003;
    public static final int FILE_PERMISSION_REQUEST = 1004;
    
    // Message types
    public static final int MESSAGE_TYPE_TEXT = 0;
    public static final int MESSAGE_TYPE_FILE = 1;
    public static final int MESSAGE_TYPE_VIDEO = 2;
    public static final int MESSAGE_TYPE_COMMAND = 3;
    public static final int MESSAGE_TYPE_SYSTEM = 4;
    
    // Terminal commands
    public static final String CMD_SCAN = "scan";
    public static final String CMD_TRACE = "trace";
    public static final String CMD_INJECT = "inject";
    public static final String CMD_CONNECT = "connect";
    public static final String CMD_DISCONNECT = "disconnect";
    public static final String CMD_HELP = "help";
    public static final String CMD_CLEAR = "clear";
    public static final String CMD_EXIT = "exit";
    public static final String CMD_STATUS = "status";
    public static final String CMD_DEVICES = "devices";
    public static final String CMD_THEME = "theme";
    public static final String CMD_SAFE_MODE = "safemode";
    public static final String CMD_UPLOAD = "upload";
    public static final String CMD_EXECUTE = "execute";
    
    // File types
    public static final String[] SCRIPT_EXTENSIONS = {".sh", ".py", ".bat", ".ps1"};
    public static final String[] VIDEO_EXTENSIONS = {".mp4", ".avi", ".mkv", ".mov"};
    public static final String[] DOCUMENT_EXTENSIONS = {".txt", ".pdf", ".doc", ".docx"};
    
    // Terminal colors (ANSI codes)
    public static final String ANSI_RESET = "\u001B[0m";
    public static final String ANSI_BLACK = "\u001B[30m";
    public static final String ANSI_RED = "\u001B[31m";
    public static final String ANSI_GREEN = "\u001B[32m";
    public static final String ANSI_YELLOW = "\u001B[33m";
    public static final String ANSI_BLUE = "\u001B[34m";
    public static final String ANSI_PURPLE = "\u001B[35m";
    public static final String ANSI_CYAN = "\u001B[36m";
    public static final String ANSI_WHITE = "\u001B[37m";
    
    // Terminal prompts
    public static final String TERMINAL_PROMPT = "hz5@cyberwolf:~$ ";
    public static final String TERMINAL_PROMPT_CONNECTED = "hz5@cyberwolf[CONNECTED]:~$ ";
    public static final String TERMINAL_PROMPT_SAFE = "hz5@cyberwolf[SAFE]:~$ ";
    public static final String TERMINAL_PROMPT_ATTACK = "hz5@cyberwolf[ATTACK]:~$ ";
    
    // System messages
    public static final String MSG_WELCOME = "Welcome to H_Z_5 Terminal CyberSuite";
    public static final String MSG_DEVELOPER_INFO = "Developed by: S. Tamilselvan - Security Researcher, CyberWolf Community";
    public static final String MSG_BLUETOOTH_ENABLED = "Bluetooth adapter enabled";
    public static final String MSG_BLUETOOTH_DISABLED = "Bluetooth adapter disabled";
    public static final String MSG_DEVICE_CONNECTED = "Device connected successfully";
    public static final String MSG_DEVICE_DISCONNECTED = "Device disconnected";
    public static final String MSG_SCANNING_DEVICES = "Scanning for nearby devices...";
    public static final String MSG_SCAN_COMPLETE = "Device scan completed";
    public static final String MSG_SAFE_MODE_ON = "Safe mode enabled - Learning environment active";
    public static final String MSG_SAFE_MODE_OFF = "Safe mode disabled - Attack simulation active";
    
    // Error messages
    public static final String ERR_BLUETOOTH_NOT_SUPPORTED = "Bluetooth not supported on this device";
    public static final String ERR_BLUETOOTH_NOT_ENABLED = "Bluetooth is not enabled";
    public static final String ERR_PERMISSION_DENIED = "Permission denied";
    public static final String ERR_CONNECTION_FAILED = "Connection failed";
    public static final String ERR_FILE_NOT_FOUND = "File not found";
    public static final String ERR_INVALID_COMMAND = "Invalid command. Type 'help' for available commands";
    public static final String ERR_DEVICE_NOT_FOUND = "Device not found";
    
    // File transfer
    public static final int BUFFER_SIZE = 1024;
    public static final String TRANSFER_FOLDER = "HZ5_Transfers";
    
    // Encryption
    public static final String ENCRYPTION_ALGORITHM = "AES";
    public static final String ENCRYPTION_TRANSFORMATION = "AES/CBC/PKCS5Padding";
    public static final int ENCRYPTION_KEY_LENGTH = 256;
    
    // Animation durations
    public static final int SPLASH_DURATION = 3000;
    public static final int TYPING_ANIMATION_DELAY = 50;
    public static final int FADE_ANIMATION_DURATION = 300;
    
    // Intent extras
    public static final String EXTRA_DEVICE_ADDRESS = "device_address";
    public static final String EXTRA_DEVICE_NAME = "device_name";
    public static final String EXTRA_FILE_PATH = "file_path";
    public static final String EXTRA_MESSAGE = "message";
    public static final String EXTRA_THEME = "theme";
}
