// Generated by view binder compiler. Do not edit!
package com.cyberwolf.hz5.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.cyberwolf.hz5.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvAppTagline;

  @NonNull
  public final TextView tvAppTitle;

  @NonNull
  public final TextView tvAsciiLogo;

  @NonNull
  public final TextView tvDeveloperInfo;

  @NonNull
  public final TextView tvDeveloperTitle;

  @NonNull
  public final TextView tvLoadingDots;

  @NonNull
  public final TextView tvLoadingText;

  @NonNull
  public final TextView tvProgressBar;

  @NonNull
  public final TextView tvVersion;

  private ActivitySplashBinding(@NonNull LinearLayout rootView, @NonNull TextView tvAppTagline,
      @NonNull TextView tvAppTitle, @NonNull TextView tvAsciiLogo,
      @NonNull TextView tvDeveloperInfo, @NonNull TextView tvDeveloperTitle,
      @NonNull TextView tvLoadingDots, @NonNull TextView tvLoadingText,
      @NonNull TextView tvProgressBar, @NonNull TextView tvVersion) {
    this.rootView = rootView;
    this.tvAppTagline = tvAppTagline;
    this.tvAppTitle = tvAppTitle;
    this.tvAsciiLogo = tvAsciiLogo;
    this.tvDeveloperInfo = tvDeveloperInfo;
    this.tvDeveloperTitle = tvDeveloperTitle;
    this.tvLoadingDots = tvLoadingDots;
    this.tvLoadingText = tvLoadingText;
    this.tvProgressBar = tvProgressBar;
    this.tvVersion = tvVersion;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_app_tagline;
      TextView tvAppTagline = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTagline == null) {
        break missingId;
      }

      id = R.id.tv_app_title;
      TextView tvAppTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTitle == null) {
        break missingId;
      }

      id = R.id.tv_ascii_logo;
      TextView tvAsciiLogo = ViewBindings.findChildViewById(rootView, id);
      if (tvAsciiLogo == null) {
        break missingId;
      }

      id = R.id.tv_developer_info;
      TextView tvDeveloperInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvDeveloperInfo == null) {
        break missingId;
      }

      id = R.id.tv_developer_title;
      TextView tvDeveloperTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDeveloperTitle == null) {
        break missingId;
      }

      id = R.id.tv_loading_dots;
      TextView tvLoadingDots = ViewBindings.findChildViewById(rootView, id);
      if (tvLoadingDots == null) {
        break missingId;
      }

      id = R.id.tv_loading_text;
      TextView tvLoadingText = ViewBindings.findChildViewById(rootView, id);
      if (tvLoadingText == null) {
        break missingId;
      }

      id = R.id.tv_progress_bar;
      TextView tvProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressBar == null) {
        break missingId;
      }

      id = R.id.tv_version;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      return new ActivitySplashBinding((LinearLayout) rootView, tvAppTagline, tvAppTitle,
          tvAsciiLogo, tvDeveloperInfo, tvDeveloperTitle, tvLoadingDots, tvLoadingText,
          tvProgressBar, tvVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
