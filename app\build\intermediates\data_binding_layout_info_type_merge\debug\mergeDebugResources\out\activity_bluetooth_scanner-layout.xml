<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_bluetooth_scanner" modulePackage="com.cyberwolf.hz5" filePath="app\src\main\res\layout\activity_bluetooth_scanner.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_bluetooth_scanner_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="14"/></Target><Target id="@+id/btn_back" view="Button"><Expressions/><location startLine="25" startOffset="8" endLine="30" endOffset="48"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="47" startOffset="8" endLine="54" endOffset="44"/></Target><Target id="@+id/btn_stop_scan" view="Button"><Expressions/><location startLine="56" startOffset="8" endLine="63" endOffset="46"/></Target><Target id="@+id/tv_scan_status" view="TextView"><Expressions/><location startLine="68" startOffset="4" endLine="76" endOffset="32"/></Target><Target id="@+id/rv_devices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="97" startOffset="4" endLine="103" endOffset="34"/></Target><Target id="@+id/btn_connect" view="Button"><Expressions/><location startLine="113" startOffset="8" endLine="121" endOffset="37"/></Target><Target id="@+id/btn_disconnect" view="Button"><Expressions/><location startLine="123" startOffset="8" endLine="131" endOffset="37"/></Target></Targets></Layout>