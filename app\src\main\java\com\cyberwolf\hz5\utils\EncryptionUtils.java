package com.cyberwolf.hz5.utils;

import android.util.Base64;
import android.util.Log;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * AES Encryption utilities for secure messaging in H_Z_5 Terminal CyberSuite
 */
public class EncryptionUtils {
    private static final String TAG = "EncryptionUtils";
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final int KEY_LENGTH = 256;
    private static final int IV_LENGTH = 16;
    
    /**
     * Generate a new AES key
     */
    public static SecretKey generateKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(KEY_LENGTH);
            return keyGenerator.generateKey();
        } catch (Exception e) {
            Log.e(TAG, "Error generating key", e);
            return null;
        }
    }
    
    /**
     * Generate a random IV (Initialization Vector)
     */
    public static byte[] generateIV() {
        byte[] iv = new byte[IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        return iv;
    }
    
    /**
     * Encrypt a message using AES encryption
     */
    public static String encrypt(String message, SecretKey key) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            byte[] iv = generateIV();
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec);
            byte[] encryptedBytes = cipher.doFinal(message.getBytes(StandardCharsets.UTF_8));
            
            // Combine IV and encrypted data
            byte[] combined = new byte[IV_LENGTH + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, IV_LENGTH);
            System.arraycopy(encryptedBytes, 0, combined, IV_LENGTH, encryptedBytes.length);
            
            return Base64.encodeToString(combined, Base64.DEFAULT);
        } catch (Exception e) {
            Log.e(TAG, "Error encrypting message", e);
            return null;
        }
    }
    
    /**
     * Decrypt a message using AES decryption
     */
    public static String decrypt(String encryptedMessage, SecretKey key) {
        try {
            byte[] combined = Base64.decode(encryptedMessage, Base64.DEFAULT);
            
            // Extract IV and encrypted data
            byte[] iv = new byte[IV_LENGTH];
            byte[] encryptedBytes = new byte[combined.length - IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, IV_LENGTH);
            System.arraycopy(combined, IV_LENGTH, encryptedBytes, 0, encryptedBytes.length);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, key, ivSpec);
            
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            Log.e(TAG, "Error decrypting message", e);
            return null;
        }
    }
    
    /**
     * Convert SecretKey to string for storage
     */
    public static String keyToString(SecretKey key) {
        return Base64.encodeToString(key.getEncoded(), Base64.DEFAULT);
    }
    
    /**
     * Convert string back to SecretKey
     */
    public static SecretKey stringToKey(String keyString) {
        try {
            byte[] keyBytes = Base64.decode(keyString, Base64.DEFAULT);
            return new SecretKeySpec(keyBytes, ALGORITHM);
        } catch (Exception e) {
            Log.e(TAG, "Error converting string to key", e);
            return null;
        }
    }
    
    /**
     * Generate a simple key from a password (for demonstration purposes)
     * Note: In production, use proper key derivation functions like PBKDF2
     */
    public static SecretKey generateKeyFromPassword(String password) {
        try {
            // Simple approach - pad or truncate password to 32 bytes
            byte[] keyBytes = new byte[32];
            byte[] passwordBytes = password.getBytes(StandardCharsets.UTF_8);
            
            if (passwordBytes.length >= 32) {
                System.arraycopy(passwordBytes, 0, keyBytes, 0, 32);
            } else {
                System.arraycopy(passwordBytes, 0, keyBytes, 0, passwordBytes.length);
                // Fill remaining bytes with zeros (already initialized)
            }
            
            return new SecretKeySpec(keyBytes, ALGORITHM);
        } catch (Exception e) {
            Log.e(TAG, "Error generating key from password", e);
            return null;
        }
    }
    
    /**
     * Encrypt file data
     */
    public static byte[] encryptBytes(byte[] data, SecretKey key) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            byte[] iv = generateIV();
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec);
            byte[] encryptedBytes = cipher.doFinal(data);
            
            // Combine IV and encrypted data
            byte[] combined = new byte[IV_LENGTH + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, IV_LENGTH);
            System.arraycopy(encryptedBytes, 0, combined, IV_LENGTH, encryptedBytes.length);
            
            return combined;
        } catch (Exception e) {
            Log.e(TAG, "Error encrypting bytes", e);
            return null;
        }
    }
    
    /**
     * Decrypt file data
     */
    public static byte[] decryptBytes(byte[] encryptedData, SecretKey key) {
        try {
            // Extract IV and encrypted data
            byte[] iv = new byte[IV_LENGTH];
            byte[] encryptedBytes = new byte[encryptedData.length - IV_LENGTH];
            System.arraycopy(encryptedData, 0, iv, 0, IV_LENGTH);
            System.arraycopy(encryptedData, IV_LENGTH, encryptedBytes, 0, encryptedBytes.length);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, key, ivSpec);
            
            return cipher.doFinal(encryptedBytes);
        } catch (Exception e) {
            Log.e(TAG, "Error decrypting bytes", e);
            return null;
        }
    }
}
