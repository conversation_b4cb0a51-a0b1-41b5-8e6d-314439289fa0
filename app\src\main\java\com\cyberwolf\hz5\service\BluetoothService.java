package com.cyberwolf.hz5.service;

import android.app.Service;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import android.util.Log;

import com.cyberwolf.hz5.bluetooth.BluetoothManager;
import com.cyberwolf.hz5.utils.Constants;

import java.util.List;

/**
 * Background Bluetooth service for H_Z_5 Terminal CyberSuite
 * Manages Bluetooth connections and messaging in the background
 */
public class BluetoothService extends Service implements BluetoothManager.BluetoothManagerListener {
    private static final String TAG = "BluetoothService";
    
    private BluetoothManager bluetoothManager;
    private BluetoothServiceListener serviceListener;
    private final IBinder binder = new BluetoothServiceBinder();
    
    public interface BluetoothServiceListener {
        void onDeviceFound(BluetoothDevice device);
        void onDiscoveryFinished(List<BluetoothDevice> devices);
        void onConnectionEstablished(BluetoothDevice device);
        void onConnectionLost();
        void onMessageReceived(String message);
        void onError(String error);
    }
    
    public class BluetoothServiceBinder extends Binder {
        public BluetoothService getService() {
            return BluetoothService.this;
        }
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        bluetoothManager = new BluetoothManager(this);
        bluetoothManager.setListener(this);
        Log.d(TAG, "BluetoothService created");
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        if (bluetoothManager != null) {
            bluetoothManager.cleanup();
        }
        Log.d(TAG, "BluetoothService destroyed");
    }
    
    // Service interface methods
    public void setServiceListener(BluetoothServiceListener listener) {
        this.serviceListener = listener;
    }
    
    public boolean isBluetoothSupported() {
        return bluetoothManager.isBluetoothSupported();
    }
    
    public boolean isBluetoothEnabled() {
        return bluetoothManager.isBluetoothEnabled();
    }
    
    public void startDiscovery() {
        bluetoothManager.startDiscovery();
    }
    
    public void stopDiscovery() {
        bluetoothManager.stopDiscovery();
    }
    
    public void connectToDevice(BluetoothDevice device) {
        bluetoothManager.connectToDevice(device);
    }
    
    public boolean sendMessage(String message) {
        return bluetoothManager.sendMessage(message);
    }
    
    public boolean sendBytes(byte[] data) {
        return bluetoothManager.sendBytes(data);
    }
    
    public void disconnect() {
        bluetoothManager.disconnect();
    }
    
    public boolean isConnected() {
        return bluetoothManager.isConnected();
    }
    
    public BluetoothDevice getConnectedDevice() {
        return bluetoothManager.getConnectedDevice();
    }
    
    public List<BluetoothDevice> getDiscoveredDevices() {
        return bluetoothManager.getDiscoveredDevices();
    }
    
    // BluetoothManager.BluetoothManagerListener implementation
    @Override
    public void onDeviceFound(BluetoothDevice device) {
        Log.d(TAG, "Device found: " + device.getName());
        if (serviceListener != null) {
            serviceListener.onDeviceFound(device);
        }
    }
    
    @Override
    public void onDiscoveryFinished(List<BluetoothDevice> devices) {
        Log.d(TAG, "Discovery finished. Found " + devices.size() + " devices");
        if (serviceListener != null) {
            serviceListener.onDiscoveryFinished(devices);
        }
    }
    
    @Override
    public void onConnectionEstablished(BluetoothDevice device) {
        Log.d(TAG, "Connection established with: " + device.getName());
        if (serviceListener != null) {
            serviceListener.onConnectionEstablished(device);
        }
    }
    
    @Override
    public void onConnectionLost() {
        Log.d(TAG, "Connection lost");
        if (serviceListener != null) {
            serviceListener.onConnectionLost();
        }
    }
    
    @Override
    public void onMessageReceived(String message) {
        Log.d(TAG, "Message received: " + message);
        if (serviceListener != null) {
            serviceListener.onMessageReceived(message);
        }
    }
    
    @Override
    public void onError(String error) {
        Log.e(TAG, "Bluetooth error: " + error);
        if (serviceListener != null) {
            serviceListener.onError(error);
        }
    }
}
