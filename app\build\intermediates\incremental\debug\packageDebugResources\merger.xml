<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="matrix_red">#FF0000</color><color name="red_shadow_warning">#FFAA00</color><color name="matrix_background">#000000</color><color name="cyber_gray_normal">#999999</color><color name="navigation_bar">#000000</color><color name="red_shadow_bright">#FF4444</color><color name="matrix_green_dim">#008800</color><color name="red_shadow_background">#0A0000</color><color name="red_shadow_dim">#882222</color><color name="cyber_gray_success">#66FF66</color><color name="warning_background">#332200</color><color name="terminal_selection">#333333</color><color name="error_background">#330000</color><color name="matrix_green_bright">#00FF00</color><color name="button_background">#333333</color><color name="cyber_gray_dim">#666666</color><color name="terminal_cursor">#FFFFFF</color><color name="cyber_gray_warning">#FFCC66</color><color name="success_background">#003300</color><color name="matrix_green_normal">#00CC00</color><color name="splash_accent">#00CCCC</color><color name="splash_text">#00FF00</color><color name="cyber_gray_bright">#CCCCCC</color><color name="status_bar">#000000</color><color name="red_shadow_normal">#CC3333</color><color name="splash_background">#000000</color><color name="button_pressed">#555555</color><color name="cyber_gray_error">#FF6666</color><color name="button_text">#FFFFFF</color><color name="red_shadow_error">#FF0000</color><color name="cyber_gray_background">#0F0F0F</color><color name="matrix_green_cyan">#00FFCC</color><color name="matrix_yellow">#FFFF00</color><color name="cyber_gray_cyan">#66CCFF</color><color name="red_shadow_success">#44FF44</color><color name="red_shadow_cyan">#FF6666</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">H_Z_5 Terminal CyberSuite</string><string name="terminal_prompt_attack">hz5@cyberwolf[ATTACK]:~$ </string><string name="developer_name">S. Tamilselvan</string><string name="file_transfer_completed">Transfer completed</string><string name="developer_title">Security Researcher, CyberWolf Community</string><string name="theme_matrix_green">Matrix Green</string><string name="menu_cybersec_lab">CyberSec Lab</string><string name="status_ready">Ready</string><string name="safe_mode_enabled">Safe Mode: ENABLED</string><string name="error_invalid_command">Invalid command</string><string name="settings_theme">Terminal Theme</string><string name="type_help">Type \'help\' for available commands</string><string name="activity_scanner">Bluetooth Scanner</string><string name="upload_script">Upload Script</string><string name="status_connecting">Connecting...</string><string name="terminal_clear">Clear</string><string name="file_transfer_progress">Transfer progress: %d%%</string><string name="activity_main">Terminal</string><string name="btn_send">Send</string><string name="theme_changed">Theme changed to %s</string><string name="btn_connect">Connect</string><string name="settings_title">Settings</string><string name="bluetooth_scan_complete">Scan completed</string><string name="btn_clear">Clear</string><string name="bluetooth_not_enabled">Bluetooth not enabled</string><string name="error_file_not_found">File not found</string><string name="error_device_not_found">Device not found</string><string name="bluetooth_permission_required">Bluetooth permission required</string><string name="about_disclaimer">This application is for educational purposes only. Use responsibly and ethically.</string><string name="about_features">Features include real-time Bluetooth messaging, file and video sharing, AES encryption, CyberSec simulation mode, and multiple terminal themes.</string><string name="safe_mode_disabled">Safe Mode: DISABLED</string><string name="menu_file_manager">File Manager</string><string name="welcome_message">Welcome to H_Z_5 Terminal CyberSuite</string><string name="btn_settings">Settings</string><string name="theme_red_shadow">Red Shadow</string><string name="script_executed">Script executed in sandbox</string><string name="btn_ok">OK</string><string name="bluetooth_scanning">Scanning for devices...</string><string name="file_transfer_started">Transfer started</string><string name="file_send">Send File</string><string name="menu_exit">Exit</string><string name="cybersec_lab_title">CyberSec Laboratory</string><string name="status_connected">Connected to %s</string><string name="about_description">H_Z_5 Terminal CyberSuite is a powerful Android application for cybersecurity enthusiasts, ethical hackers, and learners. It features terminal-based Bluetooth messaging, secure file transfers, encryption, and a simulated cybersecurity lab.</string><string name="file_transfer_failed">Transfer failed</string><string name="btn_disconnect">Disconnect</string><string name="theme_cyber_gray">Cyber Gray</string><string name="menu_settings">Settings</string><string name="menu_about">About</string><string name="activity_splash">H_Z_5</string><string name="activity_settings">Settings</string><string name="activity_cybersec_lab">CyberSec Lab</string><string name="menu_bluetooth_scanner">Bluetooth Scanner</string><string name="file_select">Select File</string><string name="btn_cancel">Cancel</string><string name="status_scanning">Scanning...</string><string name="terminal_prompt_safe">hz5@cyberwolf[SAFE]:~$ </string><string name="developer_info">Developed by: S. Tamilselvan - Security Researcher, CyberWolf Community</string><string name="about_title">About H_Z_5</string><string name="error_permission_denied">Permission denied</string><string name="settings_safe_mode">Safe Mode</string><string name="terminal_hint">Enter command...</string><string name="btn_back">Back</string><string name="app_tagline">Experience the Power of Cybersecurity in Your Pocket</string><string name="bluetooth_disconnected">Disconnected</string><string name="app_version">1.0</string><string name="terminal_prompt">hz5@cyberwolf:~$ </string><string name="settings_sound_effects">Sound Effects</string><string name="btn_scan">Scan</string><string name="bluetooth_connecting">Connecting...</string><string name="error_connection_failed">Connection failed</string><string name="terminal_prompt_connected">hz5@cyberwolf[CONNECTED]:~$ </string><string name="script_uploaded">Script uploaded successfully</string><string name="settings_about">About</string><string name="activity_file_manager">File Manager</string><string name="terminal_send">Send</string><string name="bluetooth_not_supported">Bluetooth not supported</string><string name="bluetooth_connected">Connected</string><string name="execute_script">Execute Script</string><string name="status_disconnected">Disconnected</string><string name="status_transferring">Transferring file...</string><string name="settings_auto_reconnect">Auto Reconnect</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\values\themes.xml" qualifiers=""><style name="TerminalTextStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/matrix_green_normal</item>
        <item name="android:background">@color/matrix_background</item>
        <item name="android:padding">8dp</item>
    </style><style name="TerminalCardStyle">
        <item name="android:background">@color/matrix_background</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:padding">4dp</item>
    </style><style name="Theme.HZ5.Terminal" parent="Base.Theme.HZ5">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style><style name="SplashTitleStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/splash_text</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style><style name="TerminalButtonStyle">
        <item name="android:background">@color/button_background</item>
        <item name="android:textColor">@color/button_text</item>
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">12sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:textAllCaps">true</item>
    </style><style name="SplashSubtitleStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/splash_accent</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">8dp</item>
    </style><style name="Base.Theme.HZ5" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/matrix_green_normal</item>
        <item name="colorPrimaryVariant">@color/matrix_green_dim</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/matrix_green_cyan</item>
        <item name="colorSecondaryVariant">@color/matrix_green_dim</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:colorBackground">@color/matrix_background</item>
        <item name="colorOnBackground">@color/matrix_green_normal</item>
        <item name="colorSurface">@color/matrix_background</item>
        <item name="colorOnSurface">@color/matrix_green_normal</item>
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style><style name="Theme.HZ5.Splash" parent="Base.Theme.HZ5">
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style><style name="Theme.HZ5" parent="Base.Theme.HZ5"/><style name="TerminalInputStyle" parent="TerminalTextStyle">
        <item name="android:textColorHint">@color/matrix_green_dim</item>
        <item name="android:backgroundTint">@color/matrix_green_dim</item>
        <item name="android:textCursorDrawable">@null</item>
    </style><style name="SplashDeveloperStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/matrix_green_dim</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">16dp</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.H_Z_5" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="activity_bluetooth_scanner" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\layout\activity_bluetooth_scanner.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="item_terminal_message" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\layout\item_terminal_message.xml" qualifiers="" type="layout"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\H_Z_5\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>