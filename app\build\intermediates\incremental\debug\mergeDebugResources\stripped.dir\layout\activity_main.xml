<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/matrix_background"
    tools:context=".ui.MainActivity">

    <!-- Header with app info and status -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@color/matrix_background">

        <TextView
            android:id="@+id/tv_app_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/app_name"
            android:textColor="@color/matrix_green_bright"
            android:textSize="16sp"
            android:fontFamily="monospace"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_connection_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/status_ready"
            android:textColor="@color/matrix_green_dim"
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/matrix_green_dim" />

    <!-- Terminal output area -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_terminal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/matrix_background"
        android:padding="8dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="false" />

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/matrix_green_dim" />

    <!-- Command input area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@color/matrix_background">

        <TextView
            android:id="@+id/tv_prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/terminal_prompt"
            android:textColor="@color/matrix_green_normal"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:layout_gravity="center_vertical" />

        <EditText
            android:id="@+id/et_command_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="@string/terminal_hint"
            android:textColor="@color/matrix_green_normal"
            android:textColorHint="@color/matrix_green_dim"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:background="@android:color/transparent"
            android:inputType="text"
            android:imeOptions="actionSend"
            android:layout_marginStart="4dp" />

        <Button
            android:id="@+id/btn_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/terminal_send"
            style="@style/TerminalButtonStyle"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Bottom toolbar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="4dp"
        android:background="@color/matrix_background">

        <Button
            android:id="@+id/btn_bluetooth_scanner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="BT SCAN"
            style="@style/TerminalButtonStyle"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btn_file_manager"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="FILES"
            style="@style/TerminalButtonStyle"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btn_cybersec_lab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="LAB"
            style="@style/TerminalButtonStyle"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btn_settings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="SETTINGS"
            style="@style/TerminalButtonStyle"
            android:textSize="10sp" />

        <Button
            android:id="@+id/btn_clear"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/terminal_clear"
            style="@style/TerminalButtonStyle"
            android:textSize="10sp" />

    </LinearLayout>

</LinearLayout>