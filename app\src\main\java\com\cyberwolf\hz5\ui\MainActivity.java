package com.cyberwolf.hz5.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.cyberwolf.hz5.HZ5Application;
import com.cyberwolf.hz5.R;
import com.cyberwolf.hz5.models.TerminalMessage;
import com.cyberwolf.hz5.ui.adapters.TerminalAdapter;
import com.cyberwolf.hz5.utils.CommandProcessor;
import com.cyberwolf.hz5.utils.Constants;

import java.util.ArrayList;
import java.util.List;

/**
 * Main Terminal Activity for H_Z_5 Terminal CyberSuite
 * Provides the main terminal interface with command processing
 */
public class MainActivity extends AppCompatActivity implements CommandProcessor.CommandProcessorListener {
    private static final String TAG = "MainActivity";
    
    private RecyclerView rvTerminal;
    private EditText etCommandInput;
    private TextView tvPrompt;
    private TextView tvConnectionStatus;
    private Button btnSend;
    private Button btnClear;
    
    private TerminalAdapter terminalAdapter;
    private List<TerminalMessage> messages;
    private CommandProcessor commandProcessor;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        setupTerminal();
        setupCommandProcessor();
        showWelcomeMessage();
    }
    
    private void initializeViews() {
        rvTerminal = findViewById(R.id.rv_terminal);
        etCommandInput = findViewById(R.id.et_command_input);
        tvPrompt = findViewById(R.id.tv_prompt);
        tvConnectionStatus = findViewById(R.id.tv_connection_status);
        btnSend = findViewById(R.id.btn_send);
        btnClear = findViewById(R.id.btn_clear);
        
        // Bottom toolbar buttons
        Button btnBluetoothScanner = findViewById(R.id.btn_bluetooth_scanner);
        Button btnFileManager = findViewById(R.id.btn_file_manager);
        Button btnCyberSecLab = findViewById(R.id.btn_cybersec_lab);
        Button btnSettings = findViewById(R.id.btn_settings);
        
        // Set click listeners
        btnSend.setOnClickListener(v -> processCommand());
        btnClear.setOnClickListener(v -> clearTerminal());
        
        btnBluetoothScanner.setOnClickListener(v -> 
            startActivity(new Intent(this, BluetoothScannerActivity.class)));
        btnFileManager.setOnClickListener(v -> 
            startActivity(new Intent(this, FileManagerActivity.class)));
        btnCyberSecLab.setOnClickListener(v -> 
            startActivity(new Intent(this, CyberSecLabActivity.class)));
        btnSettings.setOnClickListener(v -> 
            startActivity(new Intent(this, SettingsActivity.class)));
        
        // Set up command input
        etCommandInput.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEND || 
                (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                processCommand();
                return true;
            }
            return false;
        });
    }
    
    private void setupTerminal() {
        messages = new ArrayList<>();
        terminalAdapter = new TerminalAdapter(this, messages);
        
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setStackFromEnd(true);
        rvTerminal.setLayoutManager(layoutManager);
        rvTerminal.setAdapter(terminalAdapter);
        
        updatePrompt();
        updateConnectionStatus("Ready");
    }
    
    private void setupCommandProcessor() {
        commandProcessor = new CommandProcessor(this);
        commandProcessor.setListener(this);
    }
    
    private void showWelcomeMessage() {
        addMessage(new TerminalMessage(Constants.MSG_WELCOME, TerminalMessage.TYPE_SYSTEM));
        addMessage(new TerminalMessage(Constants.MSG_DEVELOPER_INFO, TerminalMessage.TYPE_SYSTEM));
        addMessage(new TerminalMessage("Type 'help' for available commands", TerminalMessage.TYPE_SYSTEM));
        addMessage(new TerminalMessage("", TerminalMessage.TYPE_SYSTEM));
    }
    
    private void processCommand() {
        String command = etCommandInput.getText().toString().trim();
        if (command.isEmpty()) {
            return;
        }
        
        // Add user input to terminal
        addMessage(new TerminalMessage(tvPrompt.getText().toString() + command, TerminalMessage.TYPE_USER_INPUT));
        
        // Clear input
        etCommandInput.setText("");
        
        // Process command
        if (command.equals("clear")) {
            clearTerminal();
        } else {
            commandProcessor.processCommand(command);
        }
    }
    
    private void clearTerminal() {
        messages.clear();
        terminalAdapter.notifyDataSetChanged();
        showWelcomeMessage();
    }
    
    private void addMessage(TerminalMessage message) {
        messages.add(message);
        terminalAdapter.notifyItemInserted(messages.size() - 1);
        rvTerminal.scrollToPosition(messages.size() - 1);
    }
    
    private void updatePrompt() {
        boolean isSafeMode = HZ5Application.getInstance().isSafeModeEnabled();
        String prompt = isSafeMode ? Constants.TERMINAL_PROMPT_SAFE : Constants.TERMINAL_PROMPT;
        tvPrompt.setText(prompt);
    }
    
    private void updateConnectionStatus(String status) {
        tvConnectionStatus.setText(status);
    }
    
    // CommandProcessor.CommandProcessorListener implementation
    @Override
    public void onCommandResult(TerminalMessage message) {
        runOnUiThread(() -> {
            addMessage(message);
            if (message.getMessage().contains("Safe mode")) {
                updatePrompt();
            }
        });
    }
    
    @Override
    public void onMultipleResults(List<TerminalMessage> messageList) {
        runOnUiThread(() -> {
            for (TerminalMessage message : messageList) {
                addMessage(message);
            }
        });
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // Update theme if changed
        terminalAdapter.updateTheme(HZ5Application.getInstance().getCurrentTheme());
        updatePrompt();
    }
}
