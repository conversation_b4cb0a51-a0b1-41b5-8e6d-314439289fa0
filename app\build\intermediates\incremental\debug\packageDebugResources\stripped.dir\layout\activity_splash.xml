<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/splash_background"
    android:gravity="center"
    android:padding="32dp">

    <!-- ASCII Art or Logo Area -->
    <TextView
        android:id="@+id/tv_ascii_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="██╗  ██╗    ███████╗    ███████╗\n██║  ██║    ╚══███╔╝    ██╔════╝\n███████║       ███╔╝     ███████╗\n██╔══██║      ███╔╝      ╚════██║\n██║  ██║     ███████╗    ███████║\n╚═╝  ╚═╝     ╚══════╝    ╚══════╝"
        android:textColor="@color/splash_text"
        android:textSize="16sp"
        android:fontFamily="monospace"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- App Title -->
    <TextView
        android:id="@+id/tv_app_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        style="@style/SplashTitleStyle"
        android:layout_marginBottom="8dp" />

    <!-- App Tagline -->
    <TextView
        android:id="@+id/tv_app_tagline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_tagline"
        style="@style/SplashSubtitleStyle"
        android:layout_marginBottom="32dp" />

    <!-- Loading Animation Area -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="32dp">

        <TextView
            android:id="@+id/tv_loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Initializing CyberSuite"
            android:textColor="@color/splash_accent"
            android:textSize="14sp"
            android:fontFamily="monospace" />

        <TextView
            android:id="@+id/tv_loading_dots"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="..."
            android:textColor="@color/splash_accent"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- Progress Bar (Terminal Style) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="32dp">

        <TextView
            android:id="@+id/tv_progress_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="[████████████████████████████████████████] 100%"
            android:textColor="@color/matrix_green_dim"
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:gravity="center" />

    </LinearLayout>

    <!-- Spacer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Developer Info -->
    <TextView
        android:id="@+id/tv_developer_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/developer_info"
        style="@style/SplashDeveloperStyle" />

    <TextView
        android:id="@+id/tv_developer_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/developer_title"
        style="@style/SplashDeveloperStyle"
        android:layout_marginTop="4dp" />

    <!-- Version Info -->
    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Version 1.0"
        android:textColor="@color/matrix_green_dim"
        android:textSize="10sp"
        android:fontFamily="monospace"
        android:layout_marginTop="16dp" />

</LinearLayout>
