<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme -->
    <style name="Base.Theme.HZ5" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/matrix_green_normal</item>
        <item name="colorPrimaryVariant">@color/matrix_green_dim</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/matrix_green_cyan</item>
        <item name="colorSecondaryVariant">@color/matrix_green_dim</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:colorBackground">@color/matrix_background</item>
        <item name="colorOnBackground">@color/matrix_green_normal</item>
        <item name="colorSurface">@color/matrix_background</item>
        <item name="colorOnSurface">@color/matrix_green_normal</item>
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>

    <!-- Main theme -->
    <style name="Theme.HZ5" parent="Base.Theme.HZ5" />

    <!-- Splash screen theme -->
    <style name="Theme.HZ5.Splash" parent="Base.Theme.HZ5">
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style>

    <!-- Terminal theme -->
    <style name="Theme.HZ5.Terminal" parent="Base.Theme.HZ5">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <!-- Terminal text style -->
    <style name="TerminalTextStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/matrix_green_normal</item>
        <item name="android:background">@color/matrix_background</item>
        <item name="android:padding">8dp</item>
    </style>

    <!-- Terminal input style -->
    <style name="TerminalInputStyle" parent="TerminalTextStyle">
        <item name="android:textColorHint">@color/matrix_green_dim</item>
        <item name="android:backgroundTint">@color/matrix_green_dim</item>
        <item name="android:textCursorDrawable">@null</item>
    </style>

    <!-- Button styles -->
    <style name="TerminalButtonStyle">
        <item name="android:background">@color/button_background</item>
        <item name="android:textColor">@color/button_text</item>
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">12sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <!-- Card style for terminal messages -->
    <style name="TerminalCardStyle">
        <item name="android:background">@color/matrix_background</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:padding">4dp</item>
    </style>

    <!-- Splash screen text styles -->
    <style name="SplashTitleStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/splash_text</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="SplashSubtitleStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/splash_accent</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">8dp</item>
    </style>

    <style name="SplashDeveloperStyle">
        <item name="android:fontFamily">monospace</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/matrix_green_dim</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">16dp</item>
    </style>
</resources>