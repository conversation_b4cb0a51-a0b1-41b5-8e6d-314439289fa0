package com.cyberwolf.hz5;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

/**
 * H_Z_5 Terminal CyberSuite Application Class
 * Developed by: S. <PERSON>van
 * Title: Security Researcher, CyberWolf Community
 */
public class HZ5Application extends Application {
    private static final String TAG = "HZ5Application";
    private static final String PREFS_NAME = "HZ5_PREFS";
    
    private static HZ5Application instance;
    private SharedPreferences preferences;
    
    // App constants
    public static final String APP_NAME = "H_Z_5 – Terminal CyberSuite";
    public static final String APP_VERSION = "1.0";
    public static final String DEVELOPER_NAME = "S. Tamilselvan";
    public static final String DEVELOPER_TITLE = "Security Researcher, CyberWolf Community";
    public static final String APP_TAGLINE = "Experience the Power of Cybersecurity in Your Pocket";
    
    // Theme constants
    public static final int THEME_MATRIX_GREEN = 0;
    public static final int THEME_RED_SHADOW = 1;
    public static final int THEME_CYBER_GRAY = 2;
    
    // Preference keys
    public static final String PREF_CURRENT_THEME = "current_theme";
    public static final String PREF_SOUND_EFFECTS = "sound_effects";
    public static final String PREF_AUTO_RECONNECT = "auto_reconnect";
    public static final String PREF_SAFE_MODE = "safe_mode";
    public static final String PREF_LAST_CONNECTED_DEVICE = "last_connected_device";
    
    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        preferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        Log.i(TAG, "H_Z_5 Terminal CyberSuite initialized");
        Log.i(TAG, "Developer: " + DEVELOPER_NAME);
        Log.i(TAG, "Version: " + APP_VERSION);
        
        // Initialize default preferences
        initializeDefaultPreferences();
    }
    
    public static HZ5Application getInstance() {
        return instance;
    }
    
    public SharedPreferences getPreferences() {
        return preferences;
    }
    
    private void initializeDefaultPreferences() {
        if (!preferences.contains(PREF_CURRENT_THEME)) {
            preferences.edit().putInt(PREF_CURRENT_THEME, THEME_MATRIX_GREEN).apply();
        }
        if (!preferences.contains(PREF_SOUND_EFFECTS)) {
            preferences.edit().putBoolean(PREF_SOUND_EFFECTS, true).apply();
        }
        if (!preferences.contains(PREF_AUTO_RECONNECT)) {
            preferences.edit().putBoolean(PREF_AUTO_RECONNECT, true).apply();
        }
        if (!preferences.contains(PREF_SAFE_MODE)) {
            preferences.edit().putBoolean(PREF_SAFE_MODE, true).apply();
        }
    }
    
    // Utility methods for theme management
    public int getCurrentTheme() {
        return preferences.getInt(PREF_CURRENT_THEME, THEME_MATRIX_GREEN);
    }
    
    public void setCurrentTheme(int theme) {
        preferences.edit().putInt(PREF_CURRENT_THEME, theme).apply();
    }
    
    public boolean isSoundEffectsEnabled() {
        return preferences.getBoolean(PREF_SOUND_EFFECTS, true);
    }
    
    public boolean isAutoReconnectEnabled() {
        return preferences.getBoolean(PREF_AUTO_RECONNECT, true);
    }
    
    public boolean isSafeModeEnabled() {
        return preferences.getBoolean(PREF_SAFE_MODE, true);
    }
    
    public String getLastConnectedDevice() {
        return preferences.getString(PREF_LAST_CONNECTED_DEVICE, null);
    }
    
    public void setLastConnectedDevice(String deviceAddress) {
        preferences.edit().putString(PREF_LAST_CONNECTED_DEVICE, deviceAddress).apply();
    }
}
