package com.cyberwolf.hz5.bluetooth;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.cyberwolf.hz5.utils.Constants;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * Bluetooth Manager for H_Z_5 Terminal CyberSuite
 * Handles Bluetooth operations including scanning, connecting, and messaging
 */
public class BluetoothManager {
    private static final String TAG = "BluetoothManager";
    private static final UUID SERVICE_UUID = UUID.fromString(Constants.BLUETOOTH_SERVICE_UUID);
    
    private Context context;
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothSocket socket;
    private BluetoothDevice connectedDevice;
    private InputStream inputStream;
    private OutputStream outputStream;
    private boolean isConnected = false;
    
    private List<BluetoothDevice> discoveredDevices;
    private BluetoothManagerListener listener;
    
    // Broadcast receiver for device discovery
    private final BroadcastReceiver discoveryReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (device != null && !discoveredDevices.contains(device)) {
                    discoveredDevices.add(device);
                    if (listener != null) {
                        listener.onDeviceFound(device);
                    }
                }
            } else if (BluetoothAdapter.ACTION_DISCOVERY_FINISHED.equals(action)) {
                if (listener != null) {
                    listener.onDiscoveryFinished(discoveredDevices);
                }
            }
        }
    };
    
    public interface BluetoothManagerListener {
        void onDeviceFound(BluetoothDevice device);
        void onDiscoveryFinished(List<BluetoothDevice> devices);
        void onConnectionEstablished(BluetoothDevice device);
        void onConnectionLost();
        void onMessageReceived(String message);
        void onError(String error);
    }
    
    public BluetoothManager(Context context) {
        this.context = context;
        this.bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        this.discoveredDevices = new ArrayList<>();
        
        // Register discovery receiver
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        context.registerReceiver(discoveryReceiver, filter);
    }
    
    public void setListener(BluetoothManagerListener listener) {
        this.listener = listener;
    }
    
    public boolean isBluetoothSupported() {
        return bluetoothAdapter != null;
    }
    
    public boolean isBluetoothEnabled() {
        return bluetoothAdapter != null && bluetoothAdapter.isEnabled();
    }
    
    public boolean hasBluetoothPermissions() {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) 
                == PackageManager.PERMISSION_GRANTED &&
               ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    public void startDiscovery() {
        if (!hasBluetoothPermissions()) {
            if (listener != null) {
                listener.onError(Constants.ERR_PERMISSION_DENIED);
            }
            return;
        }
        
        if (!isBluetoothEnabled()) {
            if (listener != null) {
                listener.onError(Constants.ERR_BLUETOOTH_NOT_ENABLED);
            }
            return;
        }
        
        discoveredDevices.clear();
        
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
        
        bluetoothAdapter.startDiscovery();
        Log.d(TAG, "Started Bluetooth discovery");
    }
    
    public void stopDiscovery() {
        if (hasBluetoothPermissions() && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
    }
    
    public Set<BluetoothDevice> getPairedDevices() {
        if (!hasBluetoothPermissions()) {
            return null;
        }
        return bluetoothAdapter.getBondedDevices();
    }
    
    public void connectToDevice(BluetoothDevice device) {
        new Thread(() -> {
            try {
                if (!hasBluetoothPermissions()) {
                    if (listener != null) {
                        listener.onError(Constants.ERR_PERMISSION_DENIED);
                    }
                    return;
                }
                
                // Cancel discovery to improve connection performance
                if (bluetoothAdapter.isDiscovering()) {
                    bluetoothAdapter.cancelDiscovery();
                }
                
                // Create socket
                socket = device.createRfcommSocketToServiceRecord(SERVICE_UUID);
                socket.connect();
                
                // Get streams
                inputStream = socket.getInputStream();
                outputStream = socket.getOutputStream();
                
                connectedDevice = device;
                isConnected = true;
                
                if (listener != null) {
                    listener.onConnectionEstablished(device);
                }
                
                // Start listening for messages
                startListening();
                
            } catch (IOException e) {
                Log.e(TAG, "Connection failed", e);
                if (listener != null) {
                    listener.onError(Constants.ERR_CONNECTION_FAILED);
                }
                disconnect();
            }
        }).start();
    }
    
    private void startListening() {
        new Thread(() -> {
            byte[] buffer = new byte[Constants.BUFFER_SIZE];
            int bytes;
            
            while (isConnected) {
                try {
                    bytes = inputStream.read(buffer);
                    String message = new String(buffer, 0, bytes);
                    
                    if (listener != null) {
                        listener.onMessageReceived(message);
                    }
                } catch (IOException e) {
                    Log.e(TAG, "Connection lost", e);
                    if (listener != null) {
                        listener.onConnectionLost();
                    }
                    disconnect();
                    break;
                }
            }
        }).start();
    }
    
    public boolean sendMessage(String message) {
        if (!isConnected || outputStream == null) {
            return false;
        }
        
        try {
            outputStream.write(message.getBytes());
            outputStream.flush();
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to send message", e);
            return false;
        }
    }
    
    public boolean sendBytes(byte[] data) {
        if (!isConnected || outputStream == null) {
            return false;
        }
        
        try {
            outputStream.write(data);
            outputStream.flush();
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to send bytes", e);
            return false;
        }
    }
    
    public void disconnect() {
        isConnected = false;
        
        try {
            if (inputStream != null) {
                inputStream.close();
                inputStream = null;
            }
            if (outputStream != null) {
                outputStream.close();
                outputStream = null;
            }
            if (socket != null) {
                socket.close();
                socket = null;
            }
        } catch (IOException e) {
            Log.e(TAG, "Error closing connections", e);
        }
        
        connectedDevice = null;
    }
    
    public boolean isConnected() {
        return isConnected;
    }
    
    public BluetoothDevice getConnectedDevice() {
        return connectedDevice;
    }
    
    public List<BluetoothDevice> getDiscoveredDevices() {
        return new ArrayList<>(discoveredDevices);
    }

    public void cleanup() {
        disconnect();
        stopDiscovery();

        try {
            context.unregisterReceiver(discoveryReceiver);
        } catch (IllegalArgumentException e) {
            // Receiver not registered
        }
    }
}
